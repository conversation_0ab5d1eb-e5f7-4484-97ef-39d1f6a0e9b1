"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { DatePicker, Tooltip } from "antd";
import locale from "antd/lib/date-picker/locale/tr_TR";
import dayjs from "dayjs";
import Image from "next/image";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import ConsentModal from "@/components/consent-modal";
import { QRCodeSVG } from "qrcode.react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  ErrorAlert,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { InfoCircleOutlined } from "@ant-design/icons";
import { LocationSelect } from "@/components/location-select";
// Import the API function
import { submitFormToBackend } from "@/services/api";

const formSchema = z.object({
  fullName: z.string().optional(),
  birthDate: z.date().optional(),
  height: z.string().optional(),
  weight: z.string().optional(),
  email: z.string().optional(),
  phoneNumber: z.string().optional(),
  province: z.string().optional(),
  district: z.string().optional(),
  armSpan: z.string().optional(),
  shoeSize: z.string().optional(),
  palmSize: z.string().optional(),
  hasClub: z.boolean().default(false),
  clubName: z.string().optional(),
  parentFullName: z.string().optional(),
  parentPhone: z.string().optional(),
  parentRelationshipType: z.string().optional(),
  customRelationshipType: z.string().optional(),
  motherHeight: z.string().optional(),
  fatherHeight: z.string().optional(),
  hasChronicIllness: z.boolean().default(false),
  chronicIllnessDetails: z.string().optional(),
  parentConsent: z.boolean().default(false),
  kvkkConsent: z.boolean().default(false),
});

type FormValues = z.infer<typeof formSchema>;

export default function Home() {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState<FormValues | null>(null);
  const [trackingCode, setTrackingCode] = useState<string>("");
  const [backendErrors, setBackendErrors] = useState<string[]>([]);
  const [consentModalOpen, setConsentModalOpen] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      fullName: "",
      height: "",
      weight: "",
      email: "",
      phoneNumber: "",
      armSpan: "",
      shoeSize: "",
      palmSize: "",
      hasClub: false,
      clubName: "",
      parentFullName: "",
      parentPhone: "",
      parentRelationshipType: "",
      customRelationshipType: "",
      motherHeight: "",
      fatherHeight: "",
      hasChronicIllness: false,
      chronicIllnessDetails: "",
      parentConsent: false,
      kvkkConsent: false,
    },
  });

  const getTrainingTime = (birthDate: Date): string => {
    const birthYear = birthDate.getFullYear();

    switch (birthYear) {
      case 2015:
        return "10.00 - 11.00";
      case 2014:
        return "11.00 - 12.00";
      case 2013:
        return "12.00 - 13.00";
      case 2012:
        return "13.00 - 14.00";
      case 2011:
        return "14.00 - 15.00";
      default:
        return "Belirlenmedi (Lütfen kulüple iletişime geçiniz)";
    }
  };

  const copyTrackingCode = () => {
    if (trackingCode) {
      navigator.clipboard
        .writeText(trackingCode)
        .then(() => {
          toast.success("Takip kodu başarıyla kopyalandı!", {
            description: trackingCode,
            duration: 3000,
            position: "top-center",
          });
        })
        .catch((err) => {
          toast.error("Kopyalama başarısız oldu, lütfen tekrar deneyin.", {
            position: "top-center",
          });
          console.error("Kopyalama başarısız oldu:", err);
        });
    }
  };

  async function onSubmit(values: FormValues) {
    try {
      // Clear previous errors
      setBackendErrors([]);

      // Use the imported API function
      const result = await submitFormToBackend(values);
      console.log("Form submission result:", result);

      // Get the tracking code from the response
      setTrackingCode(result.trackingCode);

      // Update state to show success message
      setFormData(values);
      setIsSubmitted(true);

      toast.success("Başvurunuz başarıyla gönderildi!", {
        position: "top-center",
        duration: 3000,
      });
    } catch (error: any) {
      console.error("Form submission error:", error);

      // Set backend validation errors if available
      if (error.response && error.response.data && error.response.data.errors) {
        const errors = error.response.data.errors;
        setBackendErrors(errors);

        // Check if the error is about email uniqueness and show a toast
        const emailError = errors.find((err: string) =>
          err.includes("e-posta adresi ile daha önce kayıt")
        );

        if (emailError) {
          toast.error(emailError, {
            position: "top-center",
            duration: 4000,
          });

          // Also set field-level error for email
          form.setError("email", {
            type: "manual",
            message: emailError,
          });
        }
      } else {
        toast.error(
          "Başvuru gönderilirken bir hata oluştu. Lütfen tekrar deneyin.",
          {
            position: "top-center",
          }
        );
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#0B478D] via-[#1A67A3] to-[#003366] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        {!isSubmitted ? (
          <Card className="shadow-2xl border-0 overflow-hidden bg-white/95 backdrop-blur-sm">
            {/* İZEFE header with logo effect */}
            <div className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] text-white p-6 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-48 h-48 bg-[#FFED00] rounded-full opacity-10 transform translate-x-16 -translate-y-16"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-[#FFED00] rounded-full opacity-10 transform -translate-x-16 translate-y-8"></div>

              <div className="flex items-center gap-4">
                <Image
                  src="/logo.png"
                  alt="izefe Logo"
                  width={80}
                  height={80}
                  className="relative z-10"
                />
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold relative z-10 mb-1">
                    İZEFE SPOR KULÜBÜ
                  </h1>
                  <h2 className="text-xl md:text-2xl font-semibold relative z-10 mb-3">
                    Erkek Basketbol Altyapı Seçmeleri Başvuru Formu
                  </h2>
                  {/* <p className="!text-[15px] md:text-base opacity-90 relative z-10">
                    İzefe Spor Kulübü Basketbol Şubesi&apos;nin düzenlediği
                    seçmeler için başvuru formu.
                  </p> */}
                </div>
              </div>
            </div>

            <CardContent className="pt-8 pb-6">
              {/* Display backend errors */}
              <ErrorAlert errors={backendErrors} />

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit as any)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* PERSONAL INFORMATION */}
                    <div className="space-y-4 md:col-span-2">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        KİŞİSEL BİLGİLER
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <FormField
                      control={form.control as any}
                      name="fullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            ADI - SOYADI
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Adınız ve soyadınız"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="birthDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel className="font-medium">
                            DOĞUM TARİHİ
                          </FormLabel>
                          <FormControl>
                            <DatePicker
                              locale={locale}
                              value={
                                field.value ? dayjs(field.value) : undefined
                              }
                              onChange={(date) => {
                                const selectedDate = date
                                  ? date.toDate()
                                  : null;
                                field.onChange(selectedDate);

                                // Check if the selected date is outside the valid range and show toast
                                if (selectedDate) {
                                  const birthYear = selectedDate.getFullYear();
                                  if (birthYear < 2011 || birthYear > 2016) {
                                    toast.error(
                                      "Başvuru yaş aralığı: Sadece 2011-2016 arası doğumlular başvuru yapabilir.",
                                      {
                                        position: "top-center",
                                        duration: 4000,
                                      }
                                    );
                                  }
                                }
                              }}
                              disabledDate={(current) =>
                                current && current.valueOf() > Date.now()
                              }
                              format="DD/MM/YYYY"
                              placeholder="Tarih seçin"
                              style={{
                                width: "100%",
                                backgroundColor: "#F3F7FA",
                              }}
                              className="border rounded-md h-10 px-3 py-2"
                              popupClassName="fenerbahce-datepicker"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">TELEFON</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Telefon numaranız"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">E-POSTA</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              placeholder="E-posta adresiniz"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* LOCATION SELECTION */}
                    <div className="space-y-4 md:col-span-2">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        ADRES BİLGİLERİ
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <div className="md:col-span-2">
                      <LocationSelect
                        provinceValue={form.watch("province")}
                        districtValue={form.watch("district")}
                        onProvinceChange={(value) =>
                          form.setValue("province", value)
                        }
                        onDistrictChange={(value) =>
                          form.setValue("district", value)
                        }
                        provinceError={form.formState.errors.province?.message}
                        districtError={form.formState.errors.district?.message}
                      />
                    </div>

                    {/* PHYSICAL MEASUREMENTS */}
                    <div className="space-y-4 md:col-span-2">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        FİZİKSEL ÖLÇÜLER
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <FormField
                      control={form.control as any}
                      name="height"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            BOY (cm)
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              placeholder="Boy (cm)"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="weight"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            KİLO (kg)
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              placeholder="Kilo (kg)"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="armSpan"
                      render={({ field }) => (
                        <FormItem>
                          <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                            KULAÇ BOYU(cm)
                            <Tooltip
                              title={
                                <div>
                                  <Image
                                    src="/kulac.png"
                                    alt="Kulaç Açıklama"
                                    width={400}
                                    height={200}
                                  />
                                </div>
                              }
                              placement="right"
                              style={{ maxWidth: 420 }}
                            >
                              <InfoCircleOutlined className="text-blue-500 cursor-pointer" />
                            </Tooltip>
                          </label>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Kulaç boyu (cm)"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="palmSize"
                      render={({ field }) => (
                        <FormItem>
                          <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                            KARIŞ ÖLÇÜSÜ (cm)
                            {/* <Tooltip
                              title={
                                <div>
                                  <Image
                                    src="/avucici.png"
                                    alt="Kulaç Açıklama"
                                    width={400}
                                    height={200}
                                  />
                                </div>
                              }
                              placement="right"
                              overlayStyle={{ maxWidth: 420 }}
                            >
                              <InfoCircleOutlined className="text-blue-500 cursor-pointer" />
                            </Tooltip> */}
                          </label>
                          <FormControl>
                            <Input
                              type="number"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              placeholder="Karış ölçüsü (cm)"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="shoeSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            AYAK NUMARASI
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Ayak numarası"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* SPORTS BACKGROUND */}
                    <div className="space-y-4 md:col-span-2">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        SPOR GEÇMİŞİ
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <div className="space-y-3 relative md:col-span-2">
                      <FormLabel className="font-medium">
                        OYNADIĞI KULÜP
                      </FormLabel>
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <FormField
                            control={form.control as any}
                            name="hasClub"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <div className="space-y-1 leading-none">
                                  <FormLabel>Var</FormLabel>
                                </div>
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="no-club"
                            checked={!form.watch("hasClub")}
                            onCheckedChange={(checked) => {
                              form.setValue("hasClub", !checked);
                              if (checked) form.setValue("clubName", "");
                            }}
                          />

                          <Label className="ml-1" htmlFor="no-club">
                            Yok
                          </Label>
                        </div>
                      </div>

                      <div className="mt-4">
                        <FormField
                          control={form.control as any}
                          name="clubName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Kulübü</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Kulüp adı"
                                  className={`placeholder:text-gray-400 placeholder:font-light placeholder:text-sm ${
                                    !form.watch("hasClub")
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                  }`}
                                  disabled={!form.watch("hasClub")}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* PARENT INFORMATION */}
                    <div className="space-y-4 md:col-span-2 pt-4">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        VELİ BİLGİLERİ
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <FormField
                      control={form.control as any}
                      name="parentFullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            ADI - SOYADI
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Velinin adı ve soyadı"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="parentPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            CEP TELEFONU
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Veli telefon numarası"
                              className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="parentRelationshipType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            YAKINLIK DERECESİ
                          </FormLabel>
                          <FormControl className="bg-transparent">
                            <select
                              className="form-select w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                              {...field}
                            >
                              <option value="">Seçiniz</option>
                              <option value="Anne">Anne</option>
                              <option value="Baba">Baba</option>
                              <option value="Diğer">Diğer</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch("parentRelationshipType") === "Diğer" && (
                      <FormField
                        control={form.control as any}
                        name="customRelationshipType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-medium">
                              YAKININIZ (LÜTFEN BELİRTİNİZ)
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Yakınlık derecenizi belirtiniz"
                                className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control as any}
                        name="motherHeight"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-medium">
                              ANNE BOYU (cm)
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Anne Boy Bilgisi (cm)"
                                {...field}
                                className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="fatherHeight"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-medium">
                              BABA BOYU (cm)
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Baba Boy Bilgisi (cm)"
                                {...field}
                                className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* HEALTH INFORMATION */}
                    <div className="space-y-4 md:col-span-2 pt-4">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        SAĞLIK BİLGİLERİ
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <div className="space-y-3 md:col-span-2">
                      <FormLabel className="font-medium">
                        Herhangi bir kronik rahatsızlığı var mı?
                      </FormLabel>
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <FormField
                            control={form.control as any}
                            name="hasChronicIllness"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <div className=" leading-none">
                                  <FormLabel>Evet</FormLabel>
                                </div>
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="no-illness"
                            checked={!form.watch("hasChronicIllness")}
                            onCheckedChange={(checked) => {
                              form.setValue("hasChronicIllness", !checked);
                              if (checked)
                                form.setValue("chronicIllnessDetails", "");
                            }}
                          />
                          <Label htmlFor="no-illness" className="ml-2">
                            Hayır
                          </Label>
                        </div>
                      </div>

                      {form.watch("hasChronicIllness") && (
                        <FormField
                          control={form.control as any}
                          name="chronicIllnessDetails"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Varsa Nedir</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Kronik rahatsızlık detayları"
                                  className="placeholder:text-gray-400 placeholder:font-light placeholder:text-sm resize-none"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    {/* CONSENT */}
                    <div className="space-y-4 md:col-span-2 pt-4">
                      <h3 className="text-lg font-semibold text-[#021F4A]">
                        ONAY VE İZİNLER
                      </h3>
                      <div className="h-1 w-20 bg-[#0A4392] rounded-full mb-4"></div>
                    </div>

                    <FormField
                      control={form.control as any}
                      name="parentConsent"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2 space-y-2">
                          <div className="flex flex-row items-start space-x-3">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="ml-2">
                                Yukarıda kişisel bilgilerini girmiş olduğum{" "}
                                {form.watch("fullName") || "..."} &apos;nın
                                velisi olarak İzefe Spor Kulübü Basketbol
                                Şubesinin düzenlediği seçmelere katılmasına onay
                                veriyorum.
                              </FormLabel>
                            </div>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control as any}
                      name="kvkkConsent"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2 space-y-2">
                          <div className="flex flex-row items-start space-x-3">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked) => {
                                  if (!checked) {
                                    field.onChange(checked);
                                  } else {
                                    setConsentModalOpen(true);
                                  }
                                }}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="ml-2">
                                <span
                                  className="text-[#021F4A] !inline font-medium cursor-pointer hover:underline"
                                  onClick={() => setConsentModalOpen(true)}
                                >
                                  KVKK Aydınlatma Metnini okudum ve onaylıyorum.
                                </span>
                              </FormLabel>
                            </div>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* KVKK Consent Modal */}
                    <ConsentModal
                      open={consentModalOpen}
                      onOpenChange={setConsentModalOpen}
                      onAccept={() => {
                        form.setValue("kvkkConsent", true);
                        setConsentModalOpen(false);
                        form.trigger("kvkkConsent");
                      }}
                    />
                  </div>
                  {/* Clothing Recommendation Section */}
                  <div className="mb-2 p-4 bg-yellow-50 border  border-yellow-200 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-yellow-600 mt-0.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div className="text-left">
                        <p className="text-sm text-yellow-700 leading-relaxed">
                          Seçme günü takım renklerimize uygun spor kıyafetler
                          tercih etmenizi rica ederiz.
                        </p>
                        <p className="text-xs text-yellow-600 mt-2 italic">
                          * Spor ayakkabı ve rahat hareket edebileceğiniz
                          kıyafetler giyiniz.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-[#021F4A] to-[#0A4392] hover:from-[#0A4392] hover:to-[#021F4A] hover:cursor-pointer !text-white font-semibold py-6 rounded-md transition-all duration-300"
                    >
                      BAŞVURUYU GÖNDER
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        ) : (
          <Card className="shadow-2xl border-0 overflow-hidden bg-white/95 backdrop-blur-sm">
            <div className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] text-white p-6 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-48 h-48 bg-[#FFED00] rounded-full opacity-10 transform translate-x-16 -translate-y-16"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-[#FFED00] rounded-full opacity-10 transform -translate-x-16 translate-y-8"></div>

              <div className="flex items-center gap-4">
                <Image
                  src="/logo.png"
                  alt="İzefe  Logo"
                  width={80}
                  height={80}
                  className="relative z-10"
                />
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold relative z-10 mb-1">
                    İZEFE
                  </h1>
                  <h2 className="text-xl md:text-2xl font-semibold relative z-10 mb-3">
                    Başvurunuz Alındı
                  </h2>
                  <p className="text-sm md:text-base opacity-90 relative z-10">
                    İzefe Spor Kulübü Basketbol Şubesi seçmeleri için başvurunuz
                    başarıyla alınmıştır.
                  </p>
                </div>
              </div>
            </div>

            <CardContent className="pt-8 pb-6">
              <div className="space-y-6">
                <div className="flex flex-col items-center justify-center">
                  {/* Success icon */}
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8 text-green-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>

                  {/* QR Code - Moved to top position */}
                  <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                    <QRCodeSVG
                      value={trackingCode}
                      size={180}
                      bgColor={"#ffffff"}
                      fgColor={"#021F4A"}
                      level={"H"}
                      includeMargin={false}
                    />
                  </div>
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-semibold text-[#021F4A]">
                    Teşekkürler, {formData?.fullName}
                  </h3>
                  <p className="text-gray-600 mt-2">
                    Başvurunuz başarıyla alınmıştır. Seçme tarihleri hakkındaki
                    bilgilere aşağıdan ulaşabilirsiniz.
                  </p>

                  <div className="mt-4 p-4 bg-blue-50 border border-blue-100 rounded-lg inline-block mx-auto">
                    <p className="text-gray-700 font-medium">Takip Kodunuz:</p>
                    <p className="text-[#0A4392] text-xl font-bold tracking-wider mt-1">
                      {trackingCode}
                    </p>

                    {/* Remove duplicate QR Code */}
                    <p className="text-xs text-gray-500 mt-2">
                      QR kodu telefonunuzla tarayarak takip kodunuzu
                      kaydedebilirsiniz
                    </p>
                  </div>

                  <div className="mt-3 flex justify-center">
                    <Button
                      onClick={copyTrackingCode}
                      className="bg-[#0A4392] hover:bg-[#021F4A] hover:cursor-pointer !text-white font-medium py-2 px-4 rounded-full flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2z"
                        />
                      </svg>
                      Takip Kodunu Kopyala
                    </Button>
                  </div>

                  {/* Training Schedule Section */}
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-blue-600 mt-0.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div className="text-left">
                        <h4 className="text-sm font-semibold text-blue-800 mb-2">
                          SEÇME TARİHİ VE SAATİNİZ
                        </h4>
                        <p className="text-sm text-blue-700 font-medium mb-2">
                          📅 22 Haziran 2025 Pazar günü
                        </p>
                        <p className="text-sm text-blue-700 font-medium">
                          🕐 Antrenman saatiniz:{" "}
                          <strong>
                            {formData?.birthDate
                              ? getTrainingTime(formData.birthDate)
                              : "Belirlenmedi"}
                          </strong>
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* Clothing Recommendation Section */}
                  <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-yellow-600 mt-0.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div className="text-left">
                        <p className="text-sm text-yellow-700 leading-relaxed">
                          Seçme günü takım renklerimize uygun spor kıyafetler
                          tercih etmenizi rica ederiz.
                        </p>
                        <p className="text-xs text-yellow-600 mt-2 italic">
                          * Spor ayakkabı ve rahat hareket edebileceğiniz
                          kıyafetler giyiniz.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    onClick={() => {
                      setIsSubmitted(false);
                      form.reset();
                      setFormData(null);
                      setTrackingCode("");
                    }}
                    className="w-full bg-gradient-to-r from-[#021F4A] to-[#0A4392] hover:from-[#0A4392] hover:to-[#021F4A] hover:cursor-pointer !text-white font-semibold py-4 rounded-md transition-all duration-300"
                  >
                    YENİ BAŞVURU
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Creative Performanz Branding */}
      <div className="mt-8 flex justify-center">
        <a
          href="https://www.performa.nz"
          target="_blank"
          rel="noopener noreferrer"
          className="group relative"
        >
          <div className="flex items-center space-x-3 px-5 py-3 ">
            <div className="relative flex items-center space-x-3">
              <div className="relative">
                <Image
                  src="/performanz.webp"
                  alt="Performanz"
                  width={28}
                  height={28}
                  className="transition-transform duration-300 group-hover:rotate-12 group-hover:scale-110"
                />
              </div>

              <div className="flex flex-col">
                <span className="text-white/90 font-medium text-sm tracking-wide group-hover:text-white transition-colors">
                  Performanz Arge ve Yazılım Hiz. A.Ş.
                </span>
              </div>
            </div>
          </div>
        </a>
      </div>
    </div>
  );
}
