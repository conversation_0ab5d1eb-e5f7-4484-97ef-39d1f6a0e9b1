"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-motion";
exports.ids = ["vendor-chunks/rc-motion"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotion.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotion.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotion: () => (/* binding */ genCSSMotion)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n/* harmony import */ var _DomWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DomWrapper */ \"(ssr)/./node_modules/rc-motion/es/DomWrapper.js\");\n/* harmony import */ var _hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useStatus */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\");\n/* harmony import */ var _hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nfunction genCSSMotion(config) {\n  var transitionSupport = config;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_context__WEBPACK_IMPORTED_MODULE_8__.Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = (0,_hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(supportMotion, visible, getDomElement, props),\n      _useStatus2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = react__WEBPACK_IMPORTED_MODULE_7__.useCallback(function (node) {\n      nodeRef.current = node;\n      (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.fillRef)(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_12__.STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if ((0,_hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__.isActive)(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = (0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()((0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.isValidElement(motionChildren) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(motionChildren)) {\n      var originNodeRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.getNodeRef)(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_DomWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotion(_util_motion__WEBPACK_IMPORTED_MODULE_13__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotionList.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotionList.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotionList: () => (/* binding */ genCSSMotionList)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _util_diff__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/diff */ \"(ssr)/./node_modules/rc-motion/es/util/diff.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\n\n\n\n\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nfunction genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _CSSMotion__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(CSSMotionList, _React$Component);\n    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, entity), {}, {\n              status: _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this$props, _excluded);\n        var Component = component || react__WEBPACK_IMPORTED_MODULE_9__.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref2, _excluded2);\n          var visible = status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_ADD || status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_KEEP;\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(CSSMotion, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.parseKeys)(keys);\n        var mixedKeyEntities = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.diffKeys)(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED && entity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(react__WEBPACK_IMPORTED_MODULE_9__.Component);\n  (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotionList(_util_motion__WEBPACK_IMPORTED_MODULE_12__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0NTU01vdGlvbkxpc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNnQztBQUNyQjtBQUNHO0FBQ047QUFDb0I7QUFDMUI7QUFDTTtBQUNNO0FBQ3hFO0FBQ0E7QUFDQTtBQUMrQjtBQUNXO0FBQ2dFO0FBQ3hEO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1Asc0ZBQXNGLG1EQUFlO0FBQ3JHO0FBQ0EsSUFBSSwrRUFBUztBQUNiLGlCQUFpQixrRkFBWTtBQUM3QjtBQUNBO0FBQ0EsTUFBTSxxRkFBZTtBQUNyQiwwRUFBMEUsYUFBYTtBQUN2RjtBQUNBO0FBQ0E7QUFDQSxNQUFNLHFGQUFlLENBQUMsNEZBQXNCO0FBQzVDO0FBQ0EsT0FBTztBQUNQO0FBQ0EsTUFBTSxxRkFBZSxDQUFDLDRGQUFzQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLGFBQWE7QUFDOUQsc0JBQXNCLHVEQUFjO0FBQ3BDLGFBQWE7QUFDYixXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDhCQUE4Qix1REFBYztBQUM1QyxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0EsSUFBSSxrRkFBWTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOEZBQXdCO0FBQzlDLHFDQUFxQywyQ0FBYztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDRCQUE0QixnREFBbUI7QUFDL0M7QUFDQSx5QkFBeUIsOEZBQXdCO0FBQ2pELG1DQUFtQyxtREFBVSxlQUFlLG9EQUFXO0FBQ3ZFLDhCQUE4QixnREFBbUIsWUFBWSw4RUFBUSxHQUFHO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCw0QkFBNEIsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLFlBQVk7QUFDdEU7QUFDQSxhQUFhO0FBQ2IsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixzREFBUztBQUN4QywrQkFBK0IscURBQVE7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7O0FBRWI7QUFDQSxvREFBb0QsdURBQWMsc0JBQXNCLHNEQUFhO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRyxDQUFDLDRDQUFlO0FBQ25CLEVBQUUscUZBQWU7QUFDakI7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlFQUFlLGlCQUFpQiw0REFBaUIsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcaXplZmUtZm9ybS15ZW5pXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcQ1NTTW90aW9uTGlzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG5pbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9jbGFzc0NhbGxDaGVjayBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY2xhc3NDYWxsQ2hlY2tcIjtcbmltcG9ydCBfY3JlYXRlQ2xhc3MgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUNsYXNzXCI7XG5pbXBvcnQgX2Fzc2VydFRoaXNJbml0aWFsaXplZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXNzZXJ0VGhpc0luaXRpYWxpemVkXCI7XG5pbXBvcnQgX2luaGVyaXRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbmhlcml0c1wiO1xuaW1wb3J0IF9jcmVhdGVTdXBlciBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlU3VwZXJcIjtcbmltcG9ydCBfZGVmaW5lUHJvcGVydHkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5XCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiY29tcG9uZW50XCIsIFwiY2hpbGRyZW5cIiwgXCJvblZpc2libGVDaGFuZ2VkXCIsIFwib25BbGxSZW1vdmVkXCJdLFxuICBfZXhjbHVkZWQyID0gW1wic3RhdHVzXCJdO1xuLyogZXNsaW50IHJlYWN0L3Byb3AtdHlwZXM6IDAgKi9cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBPcmlnaW5DU1NNb3Rpb24gZnJvbSBcIi4vQ1NTTW90aW9uXCI7XG5pbXBvcnQgeyBkaWZmS2V5cywgcGFyc2VLZXlzLCBTVEFUVVNfQURELCBTVEFUVVNfS0VFUCwgU1RBVFVTX1JFTU9WRSwgU1RBVFVTX1JFTU9WRUQgfSBmcm9tIFwiLi91dGlsL2RpZmZcIjtcbmltcG9ydCB7IHN1cHBvcnRUcmFuc2l0aW9uIH0gZnJvbSBcIi4vdXRpbC9tb3Rpb25cIjtcbnZhciBNT1RJT05fUFJPUF9OQU1FUyA9IFsnZXZlbnRQcm9wcycsICd2aXNpYmxlJywgJ2NoaWxkcmVuJywgJ21vdGlvbk5hbWUnLCAnbW90aW9uQXBwZWFyJywgJ21vdGlvbkVudGVyJywgJ21vdGlvbkxlYXZlJywgJ21vdGlvbkxlYXZlSW1tZWRpYXRlbHknLCAnbW90aW9uRGVhZGxpbmUnLCAncmVtb3ZlT25MZWF2ZScsICdsZWF2ZWRDbGFzc05hbWUnLCAnb25BcHBlYXJQcmVwYXJlJywgJ29uQXBwZWFyU3RhcnQnLCAnb25BcHBlYXJBY3RpdmUnLCAnb25BcHBlYXJFbmQnLCAnb25FbnRlclN0YXJ0JywgJ29uRW50ZXJBY3RpdmUnLCAnb25FbnRlckVuZCcsICdvbkxlYXZlU3RhcnQnLCAnb25MZWF2ZUFjdGl2ZScsICdvbkxlYXZlRW5kJ107XG4vKipcbiAqIEdlbmVyYXRlIGEgQ1NTTW90aW9uTGlzdCBjb21wb25lbnQgd2l0aCBjb25maWdcbiAqIEBwYXJhbSB0cmFuc2l0aW9uU3VwcG9ydCBObyBuZWVkIHNpbmNlIENTU01vdGlvbkxpc3Qgbm8gbG9uZ2VyIGRlcGVuZHMgb24gdHJhbnNpdGlvbiBzdXBwb3J0XG4gKiBAcGFyYW0gQ1NTTW90aW9uIENTU01vdGlvbiBjb21wb25lbnRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbkNTU01vdGlvbkxpc3QodHJhbnNpdGlvblN1cHBvcnQpIHtcbiAgdmFyIENTU01vdGlvbiA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogT3JpZ2luQ1NTTW90aW9uO1xuICB2YXIgQ1NTTW90aW9uTGlzdCA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX1JlYWN0JENvbXBvbmVudCkge1xuICAgIF9pbmhlcml0cyhDU1NNb3Rpb25MaXN0LCBfUmVhY3QkQ29tcG9uZW50KTtcbiAgICB2YXIgX3N1cGVyID0gX2NyZWF0ZVN1cGVyKENTU01vdGlvbkxpc3QpO1xuICAgIGZ1bmN0aW9uIENTU01vdGlvbkxpc3QoKSB7XG4gICAgICB2YXIgX3RoaXM7XG4gICAgICBfY2xhc3NDYWxsQ2hlY2sodGhpcywgQ1NTTW90aW9uTGlzdCk7XG4gICAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgICAgYXJnc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICAgIH1cbiAgICAgIF90aGlzID0gX3N1cGVyLmNhbGwuYXBwbHkoX3N1cGVyLCBbdGhpc10uY29uY2F0KGFyZ3MpKTtcbiAgICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJzdGF0ZVwiLCB7XG4gICAgICAgIGtleUVudGl0aWVzOiBbXVxuICAgICAgfSk7XG4gICAgICAvLyBab21iaWVKOiBSZXR1cm4gdGhlIGNvdW50IG9mIHJlc3Qga2V5cy4gSXQncyBzYWZlIHRvIHJlZmFjdG9yIGlmIG5lZWQgbW9yZSBpbmZvLlxuICAgICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcInJlbW92ZUtleVwiLCBmdW5jdGlvbiAocmVtb3ZlS2V5KSB7XG4gICAgICAgIF90aGlzLnNldFN0YXRlKGZ1bmN0aW9uIChwcmV2U3RhdGUpIHtcbiAgICAgICAgICB2YXIgbmV4dEtleUVudGl0aWVzID0gcHJldlN0YXRlLmtleUVudGl0aWVzLm1hcChmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICAgICAgICBpZiAoZW50aXR5LmtleSAhPT0gcmVtb3ZlS2V5KSByZXR1cm4gZW50aXR5O1xuICAgICAgICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgZW50aXR5KSwge30sIHtcbiAgICAgICAgICAgICAgc3RhdHVzOiBTVEFUVVNfUkVNT1ZFRFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGtleUVudGl0aWVzOiBuZXh0S2V5RW50aXRpZXNcbiAgICAgICAgICB9O1xuICAgICAgICB9LCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgdmFyIGtleUVudGl0aWVzID0gX3RoaXMuc3RhdGUua2V5RW50aXRpZXM7XG4gICAgICAgICAgdmFyIHJlc3RLZXlzQ291bnQgPSBrZXlFbnRpdGllcy5maWx0ZXIoZnVuY3Rpb24gKF9yZWYpIHtcbiAgICAgICAgICAgIHZhciBzdGF0dXMgPSBfcmVmLnN0YXR1cztcbiAgICAgICAgICAgIHJldHVybiBzdGF0dXMgIT09IFNUQVRVU19SRU1PVkVEO1xuICAgICAgICAgIH0pLmxlbmd0aDtcbiAgICAgICAgICBpZiAocmVzdEtleXNDb3VudCA9PT0gMCAmJiBfdGhpcy5wcm9wcy5vbkFsbFJlbW92ZWQpIHtcbiAgICAgICAgICAgIF90aGlzLnByb3BzLm9uQWxsUmVtb3ZlZCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBfdGhpcztcbiAgICB9XG4gICAgX2NyZWF0ZUNsYXNzKENTU01vdGlvbkxpc3QsIFt7XG4gICAgICBrZXk6IFwicmVuZGVyXCIsXG4gICAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyKCkge1xuICAgICAgICB2YXIgX3RoaXMyID0gdGhpcztcbiAgICAgICAgdmFyIGtleUVudGl0aWVzID0gdGhpcy5zdGF0ZS5rZXlFbnRpdGllcztcbiAgICAgICAgdmFyIF90aGlzJHByb3BzID0gdGhpcy5wcm9wcyxcbiAgICAgICAgICBjb21wb25lbnQgPSBfdGhpcyRwcm9wcy5jb21wb25lbnQsXG4gICAgICAgICAgY2hpbGRyZW4gPSBfdGhpcyRwcm9wcy5jaGlsZHJlbixcbiAgICAgICAgICBfb25WaXNpYmxlQ2hhbmdlZCA9IF90aGlzJHByb3BzLm9uVmlzaWJsZUNoYW5nZWQsXG4gICAgICAgICAgb25BbGxSZW1vdmVkID0gX3RoaXMkcHJvcHMub25BbGxSZW1vdmVkLFxuICAgICAgICAgIHJlc3RQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfdGhpcyRwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgICAgICAgdmFyIENvbXBvbmVudCA9IGNvbXBvbmVudCB8fCBSZWFjdC5GcmFnbWVudDtcbiAgICAgICAgdmFyIG1vdGlvblByb3BzID0ge307XG4gICAgICAgIE1PVElPTl9QUk9QX05BTUVTLmZvckVhY2goZnVuY3Rpb24gKHByb3ApIHtcbiAgICAgICAgICBtb3Rpb25Qcm9wc1twcm9wXSA9IHJlc3RQcm9wc1twcm9wXTtcbiAgICAgICAgICBkZWxldGUgcmVzdFByb3BzW3Byb3BdO1xuICAgICAgICB9KTtcbiAgICAgICAgZGVsZXRlIHJlc3RQcm9wcy5rZXlzO1xuICAgICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ29tcG9uZW50LCByZXN0UHJvcHMsIGtleUVudGl0aWVzLm1hcChmdW5jdGlvbiAoX3JlZjIsIGluZGV4KSB7XG4gICAgICAgICAgdmFyIHN0YXR1cyA9IF9yZWYyLnN0YXR1cyxcbiAgICAgICAgICAgIGV2ZW50UHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3JlZjIsIF9leGNsdWRlZDIpO1xuICAgICAgICAgIHZhciB2aXNpYmxlID0gc3RhdHVzID09PSBTVEFUVVNfQUREIHx8IHN0YXR1cyA9PT0gU1RBVFVTX0tFRVA7XG4gICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENTU01vdGlvbiwgX2V4dGVuZHMoe30sIG1vdGlvblByb3BzLCB7XG4gICAgICAgICAgICBrZXk6IGV2ZW50UHJvcHMua2V5LFxuICAgICAgICAgICAgdmlzaWJsZTogdmlzaWJsZSxcbiAgICAgICAgICAgIGV2ZW50UHJvcHM6IGV2ZW50UHJvcHMsXG4gICAgICAgICAgICBvblZpc2libGVDaGFuZ2VkOiBmdW5jdGlvbiBvblZpc2libGVDaGFuZ2VkKGNoYW5nZWRWaXNpYmxlKSB7XG4gICAgICAgICAgICAgIF9vblZpc2libGVDaGFuZ2VkID09PSBudWxsIHx8IF9vblZpc2libGVDaGFuZ2VkID09PSB2b2lkIDAgfHwgX29uVmlzaWJsZUNoYW5nZWQoY2hhbmdlZFZpc2libGUsIHtcbiAgICAgICAgICAgICAgICBrZXk6IGV2ZW50UHJvcHMua2V5XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBpZiAoIWNoYW5nZWRWaXNpYmxlKSB7XG4gICAgICAgICAgICAgICAgX3RoaXMyLnJlbW92ZUtleShldmVudFByb3BzLmtleSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KSwgZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgICAgICAgICAgIHJldHVybiBjaGlsZHJlbihfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHByb3BzKSwge30sIHtcbiAgICAgICAgICAgICAgaW5kZXg6IGluZGV4XG4gICAgICAgICAgICB9KSwgcmVmKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSkpO1xuICAgICAgfVxuICAgIH1dLCBbe1xuICAgICAga2V5OiBcImdldERlcml2ZWRTdGF0ZUZyb21Qcm9wc1wiLFxuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyhfcmVmMywgX3JlZjQpIHtcbiAgICAgICAgdmFyIGtleXMgPSBfcmVmMy5rZXlzO1xuICAgICAgICB2YXIga2V5RW50aXRpZXMgPSBfcmVmNC5rZXlFbnRpdGllcztcbiAgICAgICAgdmFyIHBhcnNlZEtleU9iamVjdHMgPSBwYXJzZUtleXMoa2V5cyk7XG4gICAgICAgIHZhciBtaXhlZEtleUVudGl0aWVzID0gZGlmZktleXMoa2V5RW50aXRpZXMsIHBhcnNlZEtleU9iamVjdHMpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGtleUVudGl0aWVzOiBtaXhlZEtleUVudGl0aWVzLmZpbHRlcihmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICAgICAgICB2YXIgcHJldkVudGl0eSA9IGtleUVudGl0aWVzLmZpbmQoZnVuY3Rpb24gKF9yZWY1KSB7XG4gICAgICAgICAgICAgIHZhciBrZXkgPSBfcmVmNS5rZXk7XG4gICAgICAgICAgICAgIHJldHVybiBlbnRpdHkua2V5ID09PSBrZXk7XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8gUmVtb3ZlIGlmIGFscmVhZHkgbWFyayBhcyByZW1vdmVkXG4gICAgICAgICAgICBpZiAocHJldkVudGl0eSAmJiBwcmV2RW50aXR5LnN0YXR1cyA9PT0gU1RBVFVTX1JFTU9WRUQgJiYgZW50aXR5LnN0YXR1cyA9PT0gU1RBVFVTX1JFTU9WRSkge1xuICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICB9KVxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH1dKTtcbiAgICByZXR1cm4gQ1NTTW90aW9uTGlzdDtcbiAgfShSZWFjdC5Db21wb25lbnQpO1xuICBfZGVmaW5lUHJvcGVydHkoQ1NTTW90aW9uTGlzdCwgXCJkZWZhdWx0UHJvcHNcIiwge1xuICAgIGNvbXBvbmVudDogJ2RpdidcbiAgfSk7XG4gIHJldHVybiBDU1NNb3Rpb25MaXN0O1xufVxuZXhwb3J0IGRlZmF1bHQgZ2VuQ1NTTW90aW9uTGlzdChzdXBwb3J0VHJhbnNpdGlvbik7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/DomWrapper.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-motion/es/DomWrapper.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(DomWrapper, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(DomWrapper);\n  function DomWrapper() {\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, DomWrapper);\n    return _super.apply(this, arguments);\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0RvbVdyYXBwZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RTtBQUNOO0FBQ047QUFDTTtBQUNuQztBQUMvQjtBQUNBLEVBQUUsK0VBQVM7QUFDWCxlQUFlLGtGQUFZO0FBQzNCO0FBQ0EsSUFBSSxxRkFBZTtBQUNuQjtBQUNBO0FBQ0EsRUFBRSxrRkFBWTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsQ0FBQyxDQUFDLDRDQUFlO0FBQ2pCLGlFQUFlLFVBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLW1vdGlvblxcZXNcXERvbVdyYXBwZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9jbGFzc0NhbGxDaGVjayBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY2xhc3NDYWxsQ2hlY2tcIjtcbmltcG9ydCBfY3JlYXRlQ2xhc3MgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUNsYXNzXCI7XG5pbXBvcnQgX2luaGVyaXRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbmhlcml0c1wiO1xuaW1wb3J0IF9jcmVhdGVTdXBlciBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlU3VwZXJcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbnZhciBEb21XcmFwcGVyID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfUmVhY3QkQ29tcG9uZW50KSB7XG4gIF9pbmhlcml0cyhEb21XcmFwcGVyLCBfUmVhY3QkQ29tcG9uZW50KTtcbiAgdmFyIF9zdXBlciA9IF9jcmVhdGVTdXBlcihEb21XcmFwcGVyKTtcbiAgZnVuY3Rpb24gRG9tV3JhcHBlcigpIHtcbiAgICBfY2xhc3NDYWxsQ2hlY2sodGhpcywgRG9tV3JhcHBlcik7XG4gICAgcmV0dXJuIF9zdXBlci5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICB9XG4gIF9jcmVhdGVDbGFzcyhEb21XcmFwcGVyLCBbe1xuICAgIGtleTogXCJyZW5kZXJcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyKCkge1xuICAgICAgcmV0dXJuIHRoaXMucHJvcHMuY2hpbGRyZW47XG4gICAgfVxuICB9XSk7XG4gIHJldHVybiBEb21XcmFwcGVyO1xufShSZWFjdC5Db21wb25lbnQpO1xuZXhwb3J0IGRlZmF1bHQgRG9tV3JhcHBlcjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/DomWrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/context.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-motion/es/context.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   \"default\": () => (/* binding */ MotionProvider)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _excluded = [\"children\"];\n\nvar Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nfunction MotionProvider(_ref) {\n  var children = _ref.children,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Context.Provider, {\n    value: props\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEY7QUFDMUY7QUFDK0I7QUFDeEIsMkJBQTJCLGdEQUFtQixHQUFHO0FBQ3pDO0FBQ2Y7QUFDQSxZQUFZLDhGQUF3QjtBQUNwQyxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxpemVmZS1mb3JtLXllbmlcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxjb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiY2hpbGRyZW5cIl07XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIENvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dCh7fSk7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNb3Rpb25Qcm92aWRlcihfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgcHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3JlZiwgX2V4Y2x1ZGVkKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogcHJvcHNcbiAgfSwgY2hpbGRyZW4pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useDomMotionEvents.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (onInternalMotionEnd) {\n  var cacheElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__[\"default\"])() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDtBQUNFOztBQUVuRDtBQUNBLGdDQUFnQyxvRUFBUyxLQUFLLGtEQUFlLEdBQUcsNENBQVM7QUFDekUsaUVBQWUseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxpemVmZS1mb3JtLXllbmlcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxob29rc1xcdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2FuVXNlRG9tIGZyb20gXCJyYy11dGlsL2VzL0RvbS9jYW5Vc2VEb21cIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG4vLyBJdCdzIHNhZmUgdG8gdXNlIGB1c2VMYXlvdXRFZmZlY3RgIGJ1dCB0aGUgd2FybmluZyBpcyBhbm5veWluZ1xudmFyIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgPSBjYW5Vc2VEb20oKSA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcbmV4cG9ydCBkZWZhdWx0IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3Q7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useNextFrame.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function () {\n  var nextFrameRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  function cancelNextFrame() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"].cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZU5leHRGcmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWlDO0FBQ0Y7QUFDL0IsaUVBQWdCO0FBQ2hCLHFCQUFxQix5Q0FBWTtBQUNqQztBQUNBLElBQUksc0RBQUc7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLW1vdGlvblxcZXNcXGhvb2tzXFx1c2VOZXh0RnJhbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHJhZiBmcm9tIFwicmMtdXRpbC9lcy9yYWZcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBkZWZhdWx0IChmdW5jdGlvbiAoKSB7XG4gIHZhciBuZXh0RnJhbWVSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gIGZ1bmN0aW9uIGNhbmNlbE5leHRGcmFtZSgpIHtcbiAgICByYWYuY2FuY2VsKG5leHRGcmFtZVJlZi5jdXJyZW50KTtcbiAgfVxuICBmdW5jdGlvbiBuZXh0RnJhbWUoY2FsbGJhY2spIHtcbiAgICB2YXIgZGVsYXkgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IDI7XG4gICAgY2FuY2VsTmV4dEZyYW1lKCk7XG4gICAgdmFyIG5leHRGcmFtZUlkID0gcmFmKGZ1bmN0aW9uICgpIHtcbiAgICAgIGlmIChkZWxheSA8PSAxKSB7XG4gICAgICAgIGNhbGxiYWNrKHtcbiAgICAgICAgICBpc0NhbmNlbGVkOiBmdW5jdGlvbiBpc0NhbmNlbGVkKCkge1xuICAgICAgICAgICAgcmV0dXJuIG5leHRGcmFtZUlkICE9PSBuZXh0RnJhbWVSZWYuY3VycmVudDtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbmV4dEZyYW1lKGNhbGxiYWNrLCBkZWxheSAtIDEpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIG5leHRGcmFtZVJlZi5jdXJyZW50ID0gbmV4dEZyYW1lSWQ7XG4gIH1cbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgY2FuY2VsTmV4dEZyYW1lKCk7XG4gICAgfTtcbiAgfSwgW10pO1xuICByZXR1cm4gW25leHRGcmFtZSwgY2FuY2VsTmV4dEZyYW1lXTtcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStatus.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useStatus)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util */ \"(ssr)/./node_modules/rc-util/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useSyncState */ \"(ssr)/./node_modules/rc-util/es/hooks/useSyncState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useDomMotionEvents */ \"(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useStepQueue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = (0,rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n  var deadlineRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = (0,rc_util__WEBPACK_IMPORTED_MODULE_3__.useEvent)(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = (0,_useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(onInternalMotionEnd),\n    _useDomMotionEvents2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onAppearPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onAppearStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onAppearActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onEnterPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onEnterStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onEnterActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onLeavePrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onLeaveStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE) {\n        var onPrepare = eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE];\n        if (!onPrepare) {\n          return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE && currentStatus !== _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.DoStep;\n    }),\n    _useStepQueue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__.isActive)(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  var visibleRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // Update with new status\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    // When use Suspense, the `visible` will repeat trigger,\n    // But not real change of the `visible`, we need to skip it.\n    // https://github.com/ant-design/ant-design/issues/44379\n    if (mountedRef.current && visibleRef.current === visible) {\n      return;\n    }\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n    visibleRef.current = visible;\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (\n    // Cancel appear\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && !motionLeave) {\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE] && step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START) {\n    mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStepQueue.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoStep: () => (/* binding */ DoStep),\n/* harmony export */   SkipStep: () => (/* binding */ SkipStep),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isActive: () => (/* binding */ isActive)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useNextFrame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useNextFrame */ \"(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\");\n\n\n\n\n\n\nvar FULL_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_START, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARED];\n\n/** Skip current step */\nvar SkipStep = false;\n/** Current step should be update in */\nvar DoStep = true;\nfunction isActive(step) {\n  return step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE || step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (status, prepareOnly, callback) {\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = (0,_useNextFrame__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n    _useNextFrame2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n    if (step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE && step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-motion/es/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSMotionList: () => (/* reexport safe */ _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Provider: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CSSMotionList */ \"(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_CSSMotion__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFvQztBQUNRO0FBQ0k7QUFDdkI7QUFDekIsaUVBQWUsa0RBQVMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLW1vdGlvblxcZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDU1NNb3Rpb24gZnJvbSBcIi4vQ1NTTW90aW9uXCI7XG5pbXBvcnQgQ1NTTW90aW9uTGlzdCBmcm9tIFwiLi9DU1NNb3Rpb25MaXN0XCI7XG5leHBvcnQgeyBkZWZhdWx0IGFzIFByb3ZpZGVyIH0gZnJvbSBcIi4vY29udGV4dFwiO1xuZXhwb3J0IHsgQ1NTTW90aW9uTGlzdCB9O1xuZXhwb3J0IGRlZmF1bHQgQ1NTTW90aW9uOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/interface.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/interface.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_APPEAR: () => (/* binding */ STATUS_APPEAR),\n/* harmony export */   STATUS_ENTER: () => (/* binding */ STATUS_ENTER),\n/* harmony export */   STATUS_LEAVE: () => (/* binding */ STATUS_LEAVE),\n/* harmony export */   STATUS_NONE: () => (/* binding */ STATUS_NONE),\n/* harmony export */   STEP_ACTIVATED: () => (/* binding */ STEP_ACTIVATED),\n/* harmony export */   STEP_ACTIVE: () => (/* binding */ STEP_ACTIVE),\n/* harmony export */   STEP_NONE: () => (/* binding */ STEP_NONE),\n/* harmony export */   STEP_PREPARE: () => (/* binding */ STEP_PREPARE),\n/* harmony export */   STEP_PREPARED: () => (/* binding */ STEP_PREPARED),\n/* harmony export */   STEP_START: () => (/* binding */ STEP_START)\n/* harmony export */ });\nvar STATUS_NONE = 'none';\nvar STATUS_APPEAR = 'appear';\nvar STATUS_ENTER = 'enter';\nvar STATUS_LEAVE = 'leave';\nvar STEP_NONE = 'none';\nvar STEP_PREPARE = 'prepare';\nvar STEP_START = 'start';\nvar STEP_ACTIVE = 'active';\nvar STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nvar STEP_PREPARED = 'prepared';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2ludGVyZmFjZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcaXplZmUtZm9ybS15ZW5pXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcaW50ZXJmYWNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgU1RBVFVTX05PTkUgPSAnbm9uZSc7XG5leHBvcnQgdmFyIFNUQVRVU19BUFBFQVIgPSAnYXBwZWFyJztcbmV4cG9ydCB2YXIgU1RBVFVTX0VOVEVSID0gJ2VudGVyJztcbmV4cG9ydCB2YXIgU1RBVFVTX0xFQVZFID0gJ2xlYXZlJztcbmV4cG9ydCB2YXIgU1RFUF9OT05FID0gJ25vbmUnO1xuZXhwb3J0IHZhciBTVEVQX1BSRVBBUkUgPSAncHJlcGFyZSc7XG5leHBvcnQgdmFyIFNURVBfU1RBUlQgPSAnc3RhcnQnO1xuZXhwb3J0IHZhciBTVEVQX0FDVElWRSA9ICdhY3RpdmUnO1xuZXhwb3J0IHZhciBTVEVQX0FDVElWQVRFRCA9ICdlbmQnO1xuLyoqXG4gKiBVc2VkIGZvciBkaXNhYmxlZCBtb3Rpb24gY2FzZS5cbiAqIFByZXBhcmUgc3RhZ2Ugd2lsbCBzdGlsbCB3b3JrIGJ1dCBzdGFydCAmIGFjdGl2ZSB3aWxsIGJlIHNraXBwZWQuXG4gKi9cbmV4cG9ydCB2YXIgU1RFUF9QUkVQQVJFRCA9ICdwcmVwYXJlZCc7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/interface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/diff.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/util/diff.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_ADD: () => (/* binding */ STATUS_ADD),\n/* harmony export */   STATUS_KEEP: () => (/* binding */ STATUS_KEEP),\n/* harmony export */   STATUS_REMOVE: () => (/* binding */ STATUS_REMOVE),\n/* harmony export */   STATUS_REMOVED: () => (/* binding */ STATUS_REMOVED),\n/* harmony export */   diffKeys: () => (/* binding */ diffKeys),\n/* harmony export */   parseKeys: () => (/* binding */ parseKeys),\n/* harmony export */   wrapKeyToObject: () => (/* binding */ wrapKeyToObject)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\n\nvar STATUS_ADD = 'add';\nvar STATUS_KEEP = 'keep';\nvar STATUS_REMOVE = 'remove';\nvar STATUS_REMOVED = 'removed';\nfunction wrapKeyToObject(key) {\n  var keyObj;\n  if (key && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nfunction parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nfunction diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/diff.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/motion.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-motion/es/util/motion.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationEndName: () => (/* binding */ animationEndName),\n/* harmony export */   getTransitionName: () => (/* binding */ getTransitionName),\n/* harmony export */   getVendorPrefixedEventName: () => (/* binding */ getVendorPrefixedEventName),\n/* harmony export */   getVendorPrefixes: () => (/* binding */ getVendorPrefixes),\n/* harmony export */   supportTransition: () => (/* binding */ supportTransition),\n/* harmony export */   transitionEndName: () => (/* binding */ transitionEndName)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n\n\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nfunction getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif ((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nfunction getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nvar supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nvar animationEndName = internalAnimationEndName || 'animationend';\nvar transitionEndName = internalTransitionEndName || 'transitionend';\nfunction getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/motion.js\n");

/***/ })

};
;