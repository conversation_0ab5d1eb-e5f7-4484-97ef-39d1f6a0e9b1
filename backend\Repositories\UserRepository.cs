using System.Text.Json;
using backend.Data;
using backend.Models;
using backend.Repositories.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace backend.Repositories;

public class UserRepository : Repository<User>, IUserRepository
{
    public UserRepository(ApplicationDbContext context)
        : base(context) { }

    public async Task<User> GetUserWithParentAsync(Guid id)
    {
        return await _context.Users.Include(u => u.Parent).FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task<User> GetUserByTrackingCodeAsync(string trackingCode)
    {
        if (trackingCode.Length < 5)
            return null;

        string[] parts = trackingCode.Split('-');
        if (parts.Length != 2)
            return null;

        if (!Guid.TryParse(parts[1], out Guid id))
            return null;

        return await GetUserWithParentAsync(id);
    }

    public async Task<IEnumerable<User>> GetUsersAsync()
    {
        // Exclude admin and moderator users by filtering out users without parent information
        // Real applicants must have parent information, while admin/moderator users don't
        var result = await _context.Users
            .ToListAsync();
        return result;
    }

    public async Task<IEnumerable<User>> GetApplicantUsersAsync()
    {
        // Get only real applicants by excluding admin and moderator users
        // Real applicants have parent information and are not in admin/moderator roles
        var applicants = await _context
            .Users.Include(u => u.Parent)
            .Where(u => u.Parent != null) // Must have parent information
            .Where(u =>
                !_context.UserRoles.Any(ur =>
                    ur.UserId == u.Id
                    && _context.Roles.Any(r =>
                        r.Id == ur.RoleId && (r.Name == "Admin" || r.Name == "Moderator")
                    )
                )
            ) // Exclude users with Admin or Moderator roles
            .ToListAsync();

        return applicants;
    }

    /// <summary>
    /// Gets the next tracking sequence number for users born in a specific year.
    /// Each birth year starts from 1 and increments for each user.
    /// </summary>
    /// <param name="birthYear">The birth year to get the next sequence for</param>
    /// <returns>The next sequence number for that birth year (starting from 1)</returns>
    public async Task<int> GetNextTrackingSequenceForBirthYearAsync(int birthYear)
    {
        // Avoid PostgreSQL integer overflow by using date range comparison instead of year extraction
        // Use UTC DateTimes to match PostgreSQL timestamp with time zone requirements
        var startOfYear = new DateTime(birthYear, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var startOfNextYear = new DateTime(birthYear + 1, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        var usersInYear = await _context
            .Users
            .Where(u => u.DateOfBirth >= startOfYear && u.DateOfBirth < startOfNextYear)
            .Select(u => u.TrackingSequence)
            .ToListAsync();

        var maxSequence = usersInYear.Any() ? usersInYear.Max() : 0;
        return maxSequence + 1;
    }

    /// <summary>
    /// [Obsolete] Use GetNextTrackingSequenceForBirthYearAsync instead for birth year based sequences
    /// </summary>
    [Obsolete(
        "Use GetNextTrackingSequenceForBirthYearAsync instead for birth year based sequences"
    )]
    public int GetNextTrackingCode()
    {
        int maxSequence = _context.Users.Max(u => u.TrackingSequence);
        return maxSequence + 1;
    }

    public async Task DeleteUserWithParentAsync(Guid userId)
    {
        var user = await GetUserWithParentAsync(userId);
        if (user == null)
            return;
        var parent = user.Parent;

        _context.Users.Remove(user);

        if (parent != null)
        {
            var parentHasOtherUsers = await _context.Users.AnyAsync(u =>
                u.ParentId == parent.Id && u.Id != userId
            );

            if (!parentHasOtherUsers)
            {
                _context.Parents.Remove(parent);
            }
        }
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        if (string.IsNullOrEmpty(email))
            return false;

        return await _context.Users.AnyAsync(u => u.Email.ToLower() == email.ToLower());
    }

    public async Task<User> GetUserByEmailAsync(string email)
    {
        if (string.IsNullOrEmpty(email))
            return null;

        return await _context
            .Users.Include(u => u.Parent)
            .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());
    }
}
