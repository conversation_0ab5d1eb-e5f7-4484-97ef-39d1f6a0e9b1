namespace backend.Models.DTOs;

public class RatingDTO
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string? FullName { get; set; }
    public Guid ModeratorId { get; set; }

    public int? ToplaBeceri { get; set; }

    public int? FizikselOzellik { get; set; }

    public string? Notes { get; set; }

    public double? JumpHeight { get; set; }

    public DateTime? JumpRecordDate { get; set; }

    public double? SprintTime { get; set; }

    public DateTime? SprintRecordDate { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateRatingDTO
{
    public Guid UserId { get; set; }

    public int? ToplaBeceri { get; set; }

    public int? FizikselOzellik { get; set; }

    public string? Notes { get; set; }

    public double? JumpHeight { get; set; }

    public DateTime? JumpRecordDate { get; set; }

    public double? SprintTime { get; set; }

    public DateTime? SprintRecordDate { get; set; }
}

public class UpdateRatingDTO
{
    public int? ToplaBeceri { get; set; }

    public int? FizikselOzellik { get; set; }

    public string? Notes { get; set; }

    public double? JumpHeight { get; set; }

    public DateTime? JumpRecordDate { get; set; }

    public double? SprintTime { get; set; }

    public DateTime? SprintRecordDate { get; set; }
}

public class RatingWithModeratorDTO
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid ModeratorId { get; set; }
    public string ModeratorName { get; set; } = string.Empty;
    public int? ToplaBeceri { get; set; }
    public int? FizikselOzellik { get; set; }
    public string? Notes { get; set; }
    public double? JumpHeight { get; set; }
    public DateTime? JumpRecordDate { get; set; }
    public double? SprintTime { get; set; }
    public DateTime? SprintRecordDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class UserRatingSummaryDTO
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public int BirthYear { get; set; }
    public List<RatingWithModeratorDTO> Ratings { get; set; } = new();
    public double? AverageToplaBeceri { get; set; }
    public double? AverageFizikselOzellik { get; set; }
    public int RatingCount { get; set; }
}
