"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-overflow";
exports.ids = ["vendor-chunks/rc-overflow"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-overflow/es/Item.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-overflow/es/Item.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n\n\n\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\n\n\n\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props, _excluded);\n  var mergedHidden = responsive && !display;\n\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item, {\n    index: order\n  }) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(!invalidate && prefixCls, className),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(InternalItem);\nItem.displayName = 'Item';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Item);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/Overflow.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-overflow/es/Overflow.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useEffectState */ \"(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\");\n/* harmony import */ var _RawItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./RawItem */ \"(ssr)/./node_modules/rc-overflow/es/RawItem.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\n\n\n\n\n\n\n\n\n\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\n\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__.useBatcher)();\n  var _useEffectState = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, null),\n    _useEffectState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, new Map()),\n    _useEffectState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n\n  // ================================= Item =================================\n  var getKey = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n      key: key,\n      value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n\n  // >>>>> Rest node\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  var mergedRenderRest = renderRest || defaultRenderRest;\n  var restNode = renderRawRest ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n    value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), restContextProps)\n  }, renderRawRest(omittedItems)) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  var overflowNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  return isResponsive ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onOverflowResize,\n    disabled: !shouldResponsive\n  }, overflowNode) : overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = _RawItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n\n// Convert to generic type\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardOverflow);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvT3ZlcmZsb3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNXO0FBQ0M7QUFDb0I7QUFDMUY7QUFDK0I7QUFDd0I7QUFDbkI7QUFDWTtBQUNlO0FBQ3JDO0FBQzBDO0FBQ3BDO0FBQ1k7QUFDNUM7QUFDQTtBQUM0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOEZBQXdCO0FBQ3hDO0FBQ0EsMkJBQTJCLGlFQUFVO0FBQ3JDLHdCQUF3QixpRUFBYztBQUN0Qyx1QkFBdUIsb0ZBQWM7QUFDckM7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHdCQUF3QixvRkFBYztBQUN0QztBQUNBO0FBQ0Esa0JBQWtCLCtDQUFRO0FBQzFCLGlCQUFpQixvRkFBYztBQUMvQjtBQUNBO0FBQ0EsbUJBQW1CLCtDQUFRO0FBQzNCLGlCQUFpQixvRkFBYztBQUMvQjtBQUNBO0FBQ0EsMkJBQTJCLDBDQUFhO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOENBQU87QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLDhDQUFPO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLGVBQWUsa0RBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCx5QkFBeUIsa0RBQVc7QUFDcEM7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSw0RUFBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLFNBQVM7QUFDL0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnREFBbUIsQ0FBQyxzREFBZTtBQUMzRDtBQUNBLGFBQWEsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLHNCQUFzQjtBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxJQUFJO0FBQ0o7QUFDQSx3QkFBd0IsZ0RBQW1CLENBQUMsNkNBQUksRUFBRSw4RUFBUSxHQUFHO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsZ0RBQW1CLENBQUMsc0RBQWU7QUFDakYsV0FBVyxvRkFBYSxDQUFDLG9GQUFhLEdBQUc7QUFDekMsR0FBRyw4Q0FBOEMsZ0RBQW1CLENBQUMsNkNBQUksRUFBRSw4RUFBUSxHQUFHO0FBQ3RGLGtDQUFrQyxnREFBbUIsWUFBWSw4RUFBUTtBQUN6RSxlQUFlLGlEQUFVO0FBQ3pCO0FBQ0E7QUFDQSxHQUFHLHlHQUF5RyxnREFBbUIsQ0FBQyw2Q0FBSSxFQUFFLDhFQUFRLEdBQUc7QUFDako7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUNBQXFDLGdEQUFtQixDQUFDLDBEQUFjO0FBQ3ZFO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxtQ0FBbUMsNkNBQWdCO0FBQ25EO0FBQ0EsdUJBQXVCLGlEQUFPO0FBQzlCO0FBQ0E7O0FBRUE7QUFDQSxpRUFBZSxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJ1cmFrXFxEZXNrdG9wXFxpemVmZS1mb3JtLXllbmlcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxyYy1vdmVyZmxvd1xcZXNcXE92ZXJmbG93LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbnZhciBfZXhjbHVkZWQgPSBbXCJwcmVmaXhDbHNcIiwgXCJkYXRhXCIsIFwicmVuZGVySXRlbVwiLCBcInJlbmRlclJhd0l0ZW1cIiwgXCJpdGVtS2V5XCIsIFwiaXRlbVdpZHRoXCIsIFwic3NyXCIsIFwic3R5bGVcIiwgXCJjbGFzc05hbWVcIiwgXCJtYXhDb3VudFwiLCBcInJlbmRlclJlc3RcIiwgXCJyZW5kZXJSYXdSZXN0XCIsIFwic3VmZml4XCIsIFwiY29tcG9uZW50XCIsIFwiaXRlbUNvbXBvbmVudFwiLCBcIm9uVmlzaWJsZUNoYW5nZVwiXTtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VNZW1vLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IFJlc2l6ZU9ic2VydmVyIGZyb20gJ3JjLXJlc2l6ZS1vYnNlcnZlcic7XG5pbXBvcnQgdXNlTGF5b3V0RWZmZWN0IGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZUxheW91dEVmZmVjdFwiO1xuaW1wb3J0IEl0ZW0gZnJvbSBcIi4vSXRlbVwiO1xuaW1wb3J0IHVzZUVmZmVjdFN0YXRlLCB7IHVzZUJhdGNoZXIgfSBmcm9tIFwiLi9ob29rcy91c2VFZmZlY3RTdGF0ZVwiO1xuaW1wb3J0IFJhd0l0ZW0gZnJvbSBcIi4vUmF3SXRlbVwiO1xuaW1wb3J0IHsgT3ZlcmZsb3dDb250ZXh0IH0gZnJvbSBcIi4vY29udGV4dFwiO1xudmFyIFJFU1BPTlNJVkUgPSAncmVzcG9uc2l2ZSc7XG52YXIgSU5WQUxJREFURSA9ICdpbnZhbGlkYXRlJztcbmV4cG9ydCB7IE92ZXJmbG93Q29udGV4dCB9IGZyb20gXCIuL2NvbnRleHRcIjtcbmZ1bmN0aW9uIGRlZmF1bHRSZW5kZXJSZXN0KG9taXR0ZWRJdGVtcykge1xuICByZXR1cm4gXCIrIFwiLmNvbmNhdChvbWl0dGVkSXRlbXMubGVuZ3RoLCBcIiAuLi5cIik7XG59XG5mdW5jdGlvbiBPdmVyZmxvdyhwcm9wcywgcmVmKSB7XG4gIHZhciBfcHJvcHMkcHJlZml4Q2xzID0gcHJvcHMucHJlZml4Q2xzLFxuICAgIHByZWZpeENscyA9IF9wcm9wcyRwcmVmaXhDbHMgPT09IHZvaWQgMCA/ICdyYy1vdmVyZmxvdycgOiBfcHJvcHMkcHJlZml4Q2xzLFxuICAgIF9wcm9wcyRkYXRhID0gcHJvcHMuZGF0YSxcbiAgICBkYXRhID0gX3Byb3BzJGRhdGEgPT09IHZvaWQgMCA/IFtdIDogX3Byb3BzJGRhdGEsXG4gICAgcmVuZGVySXRlbSA9IHByb3BzLnJlbmRlckl0ZW0sXG4gICAgcmVuZGVyUmF3SXRlbSA9IHByb3BzLnJlbmRlclJhd0l0ZW0sXG4gICAgaXRlbUtleSA9IHByb3BzLml0ZW1LZXksXG4gICAgX3Byb3BzJGl0ZW1XaWR0aCA9IHByb3BzLml0ZW1XaWR0aCxcbiAgICBpdGVtV2lkdGggPSBfcHJvcHMkaXRlbVdpZHRoID09PSB2b2lkIDAgPyAxMCA6IF9wcm9wcyRpdGVtV2lkdGgsXG4gICAgc3NyID0gcHJvcHMuc3NyLFxuICAgIHN0eWxlID0gcHJvcHMuc3R5bGUsXG4gICAgY2xhc3NOYW1lID0gcHJvcHMuY2xhc3NOYW1lLFxuICAgIG1heENvdW50ID0gcHJvcHMubWF4Q291bnQsXG4gICAgcmVuZGVyUmVzdCA9IHByb3BzLnJlbmRlclJlc3QsXG4gICAgcmVuZGVyUmF3UmVzdCA9IHByb3BzLnJlbmRlclJhd1Jlc3QsXG4gICAgc3VmZml4ID0gcHJvcHMuc3VmZml4LFxuICAgIF9wcm9wcyRjb21wb25lbnQgPSBwcm9wcy5jb21wb25lbnQsXG4gICAgQ29tcG9uZW50ID0gX3Byb3BzJGNvbXBvbmVudCA9PT0gdm9pZCAwID8gJ2RpdicgOiBfcHJvcHMkY29tcG9uZW50LFxuICAgIGl0ZW1Db21wb25lbnQgPSBwcm9wcy5pdGVtQ29tcG9uZW50LFxuICAgIG9uVmlzaWJsZUNoYW5nZSA9IHByb3BzLm9uVmlzaWJsZUNoYW5nZSxcbiAgICByZXN0UHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMocHJvcHMsIF9leGNsdWRlZCk7XG4gIHZhciBmdWxseVNTUiA9IHNzciA9PT0gJ2Z1bGwnO1xuICB2YXIgbm90aWZ5RWZmZWN0VXBkYXRlID0gdXNlQmF0Y2hlcigpO1xuICB2YXIgX3VzZUVmZmVjdFN0YXRlID0gdXNlRWZmZWN0U3RhdGUobm90aWZ5RWZmZWN0VXBkYXRlLCBudWxsKSxcbiAgICBfdXNlRWZmZWN0U3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZUVmZmVjdFN0YXRlLCAyKSxcbiAgICBjb250YWluZXJXaWR0aCA9IF91c2VFZmZlY3RTdGF0ZTJbMF0sXG4gICAgc2V0Q29udGFpbmVyV2lkdGggPSBfdXNlRWZmZWN0U3RhdGUyWzFdO1xuICB2YXIgbWVyZ2VkQ29udGFpbmVyV2lkdGggPSBjb250YWluZXJXaWR0aCB8fCAwO1xuICB2YXIgX3VzZUVmZmVjdFN0YXRlMyA9IHVzZUVmZmVjdFN0YXRlKG5vdGlmeUVmZmVjdFVwZGF0ZSwgbmV3IE1hcCgpKSxcbiAgICBfdXNlRWZmZWN0U3RhdGU0ID0gX3NsaWNlZFRvQXJyYXkoX3VzZUVmZmVjdFN0YXRlMywgMiksXG4gICAgaXRlbVdpZHRocyA9IF91c2VFZmZlY3RTdGF0ZTRbMF0sXG4gICAgc2V0SXRlbVdpZHRocyA9IF91c2VFZmZlY3RTdGF0ZTRbMV07XG4gIHZhciBfdXNlRWZmZWN0U3RhdGU1ID0gdXNlRWZmZWN0U3RhdGUobm90aWZ5RWZmZWN0VXBkYXRlLCAwKSxcbiAgICBfdXNlRWZmZWN0U3RhdGU2ID0gX3NsaWNlZFRvQXJyYXkoX3VzZUVmZmVjdFN0YXRlNSwgMiksXG4gICAgcHJldlJlc3RXaWR0aCA9IF91c2VFZmZlY3RTdGF0ZTZbMF0sXG4gICAgc2V0UHJldlJlc3RXaWR0aCA9IF91c2VFZmZlY3RTdGF0ZTZbMV07XG4gIHZhciBfdXNlRWZmZWN0U3RhdGU3ID0gdXNlRWZmZWN0U3RhdGUobm90aWZ5RWZmZWN0VXBkYXRlLCAwKSxcbiAgICBfdXNlRWZmZWN0U3RhdGU4ID0gX3NsaWNlZFRvQXJyYXkoX3VzZUVmZmVjdFN0YXRlNywgMiksXG4gICAgcmVzdFdpZHRoID0gX3VzZUVmZmVjdFN0YXRlOFswXSxcbiAgICBzZXRSZXN0V2lkdGggPSBfdXNlRWZmZWN0U3RhdGU4WzFdO1xuICB2YXIgX3VzZUVmZmVjdFN0YXRlOSA9IHVzZUVmZmVjdFN0YXRlKG5vdGlmeUVmZmVjdFVwZGF0ZSwgMCksXG4gICAgX3VzZUVmZmVjdFN0YXRlMTAgPSBfc2xpY2VkVG9BcnJheShfdXNlRWZmZWN0U3RhdGU5LCAyKSxcbiAgICBzdWZmaXhXaWR0aCA9IF91c2VFZmZlY3RTdGF0ZTEwWzBdLFxuICAgIHNldFN1ZmZpeFdpZHRoID0gX3VzZUVmZmVjdFN0YXRlMTBbMV07XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZShudWxsKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBzdWZmaXhGaXhlZFN0YXJ0ID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRTdWZmaXhGaXhlZFN0YXJ0ID0gX3VzZVN0YXRlMlsxXTtcbiAgdmFyIF91c2VTdGF0ZTMgPSB1c2VTdGF0ZShudWxsKSxcbiAgICBfdXNlU3RhdGU0ID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlMywgMiksXG4gICAgZGlzcGxheUNvdW50ID0gX3VzZVN0YXRlNFswXSxcbiAgICBzZXREaXNwbGF5Q291bnQgPSBfdXNlU3RhdGU0WzFdO1xuICB2YXIgbWVyZ2VkRGlzcGxheUNvdW50ID0gUmVhY3QudXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgaWYgKGRpc3BsYXlDb3VudCA9PT0gbnVsbCAmJiBmdWxseVNTUikge1xuICAgICAgcmV0dXJuIE51bWJlci5NQVhfU0FGRV9JTlRFR0VSO1xuICAgIH1cbiAgICByZXR1cm4gZGlzcGxheUNvdW50IHx8IDA7XG4gIH0sIFtkaXNwbGF5Q291bnQsIGNvbnRhaW5lcldpZHRoXSk7XG4gIHZhciBfdXNlU3RhdGU1ID0gdXNlU3RhdGUoZmFsc2UpLFxuICAgIF91c2VTdGF0ZTYgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGU1LCAyKSxcbiAgICByZXN0UmVhZHkgPSBfdXNlU3RhdGU2WzBdLFxuICAgIHNldFJlc3RSZWFkeSA9IF91c2VTdGF0ZTZbMV07XG4gIHZhciBpdGVtUHJlZml4Q2xzID0gXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1pdGVtXCIpO1xuXG4gIC8vIEFsd2F5cyB1c2UgdGhlIG1heCB3aWR0aCB0byBhdm9pZCBibGlua1xuICB2YXIgbWVyZ2VkUmVzdFdpZHRoID0gTWF0aC5tYXgocHJldlJlc3RXaWR0aCwgcmVzdFdpZHRoKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gRGF0YSA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIGlzUmVzcG9uc2l2ZSA9IG1heENvdW50ID09PSBSRVNQT05TSVZFO1xuICB2YXIgc2hvdWxkUmVzcG9uc2l2ZSA9IGRhdGEubGVuZ3RoICYmIGlzUmVzcG9uc2l2ZTtcbiAgdmFyIGludmFsaWRhdGUgPSBtYXhDb3VudCA9PT0gSU5WQUxJREFURTtcblxuICAvKipcbiAgICogV2hlbiBpcyBgcmVzcG9uc2l2ZWAsIHdlIHdpbGwgYWx3YXlzIHJlbmRlciByZXN0IG5vZGUgdG8gZ2V0IHRoZSByZWFsIHdpZHRoIG9mIGl0IGZvciBjYWxjdWxhdGlvblxuICAgKi9cbiAgdmFyIHNob3dSZXN0ID0gc2hvdWxkUmVzcG9uc2l2ZSB8fCB0eXBlb2YgbWF4Q291bnQgPT09ICdudW1iZXInICYmIGRhdGEubGVuZ3RoID4gbWF4Q291bnQ7XG4gIHZhciBtZXJnZWREYXRhID0gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgdmFyIGl0ZW1zID0gZGF0YTtcbiAgICBpZiAoc2hvdWxkUmVzcG9uc2l2ZSkge1xuICAgICAgaWYgKGNvbnRhaW5lcldpZHRoID09PSBudWxsICYmIGZ1bGx5U1NSKSB7XG4gICAgICAgIGl0ZW1zID0gZGF0YTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGl0ZW1zID0gZGF0YS5zbGljZSgwLCBNYXRoLm1pbihkYXRhLmxlbmd0aCwgbWVyZ2VkQ29udGFpbmVyV2lkdGggLyBpdGVtV2lkdGgpKTtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKHR5cGVvZiBtYXhDb3VudCA9PT0gJ251bWJlcicpIHtcbiAgICAgIGl0ZW1zID0gZGF0YS5zbGljZSgwLCBtYXhDb3VudCk7XG4gICAgfVxuICAgIHJldHVybiBpdGVtcztcbiAgfSwgW2RhdGEsIGl0ZW1XaWR0aCwgY29udGFpbmVyV2lkdGgsIG1heENvdW50LCBzaG91bGRSZXNwb25zaXZlXSk7XG4gIHZhciBvbWl0dGVkSXRlbXMgPSB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoc2hvdWxkUmVzcG9uc2l2ZSkge1xuICAgICAgcmV0dXJuIGRhdGEuc2xpY2UobWVyZ2VkRGlzcGxheUNvdW50ICsgMSk7XG4gICAgfVxuICAgIHJldHVybiBkYXRhLnNsaWNlKG1lcmdlZERhdGEubGVuZ3RoKTtcbiAgfSwgW2RhdGEsIG1lcmdlZERhdGEsIHNob3VsZFJlc3BvbnNpdmUsIG1lcmdlZERpc3BsYXlDb3VudF0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBJdGVtID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgZ2V0S2V5ID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7XG4gICAgdmFyIF9yZWY7XG4gICAgaWYgKHR5cGVvZiBpdGVtS2V5ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICByZXR1cm4gaXRlbUtleShpdGVtKTtcbiAgICB9XG4gICAgcmV0dXJuIChfcmVmID0gaXRlbUtleSAmJiAoaXRlbSA9PT0gbnVsbCB8fCBpdGVtID09PSB2b2lkIDAgPyB2b2lkIDAgOiBpdGVtW2l0ZW1LZXldKSkgIT09IG51bGwgJiYgX3JlZiAhPT0gdm9pZCAwID8gX3JlZiA6IGluZGV4O1xuICB9LCBbaXRlbUtleV0pO1xuICB2YXIgbWVyZ2VkUmVuZGVySXRlbSA9IHVzZUNhbGxiYWNrKHJlbmRlckl0ZW0gfHwgZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICByZXR1cm4gaXRlbTtcbiAgfSwgW3JlbmRlckl0ZW1dKTtcbiAgZnVuY3Rpb24gdXBkYXRlRGlzcGxheUNvdW50KGNvdW50LCBzdWZmaXhGaXhlZFN0YXJ0VmFsLCBub3RSZWFkeSkge1xuICAgIC8vIFJlYWN0IDE4IHdpbGwgc3luYyByZW5kZXIgZXZlbiB3aGVuIHRoZSB2YWx1ZSBpcyBzYW1lIGluIHNvbWUgY2FzZS5cbiAgICAvLyBXZSB0YWtlIGBtZXJnZWREYXRhYCBhcyBkZXBzIHdoaWNoIG1heSBjYXVzZSBkZWFkIGxvb3AgaWYgaXQncyBkeW5hbWljIGdlbmVyYXRlLlxuICAgIC8vIHJlZjogaHR0cHM6Ly9naXRodWIuY29tL2FudC1kZXNpZ24vYW50LWRlc2lnbi9pc3N1ZXMvMzY1NTlcbiAgICBpZiAoZGlzcGxheUNvdW50ID09PSBjb3VudCAmJiAoc3VmZml4Rml4ZWRTdGFydFZhbCA9PT0gdW5kZWZpbmVkIHx8IHN1ZmZpeEZpeGVkU3RhcnRWYWwgPT09IHN1ZmZpeEZpeGVkU3RhcnQpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHNldERpc3BsYXlDb3VudChjb3VudCk7XG4gICAgaWYgKCFub3RSZWFkeSkge1xuICAgICAgc2V0UmVzdFJlYWR5KGNvdW50IDwgZGF0YS5sZW5ndGggLSAxKTtcbiAgICAgIG9uVmlzaWJsZUNoYW5nZSA9PT0gbnVsbCB8fCBvblZpc2libGVDaGFuZ2UgPT09IHZvaWQgMCB8fCBvblZpc2libGVDaGFuZ2UoY291bnQpO1xuICAgIH1cbiAgICBpZiAoc3VmZml4Rml4ZWRTdGFydFZhbCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBzZXRTdWZmaXhGaXhlZFN0YXJ0KHN1ZmZpeEZpeGVkU3RhcnRWYWwpO1xuICAgIH1cbiAgfVxuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBTaXplID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICBmdW5jdGlvbiBvbk92ZXJmbG93UmVzaXplKF8sIGVsZW1lbnQpIHtcbiAgICBzZXRDb250YWluZXJXaWR0aChlbGVtZW50LmNsaWVudFdpZHRoKTtcbiAgfVxuICBmdW5jdGlvbiByZWdpc3RlclNpemUoa2V5LCB3aWR0aCkge1xuICAgIHNldEl0ZW1XaWR0aHMoZnVuY3Rpb24gKG9yaWdpbikge1xuICAgICAgdmFyIGNsb25lID0gbmV3IE1hcChvcmlnaW4pO1xuICAgICAgaWYgKHdpZHRoID09PSBudWxsKSB7XG4gICAgICAgIGNsb25lLmRlbGV0ZShrZXkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY2xvbmUuc2V0KGtleSwgd2lkdGgpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGNsb25lO1xuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIHJlZ2lzdGVyT3ZlcmZsb3dTaXplKF8sIHdpZHRoKSB7XG4gICAgc2V0UmVzdFdpZHRoKHdpZHRoKTtcbiAgICBzZXRQcmV2UmVzdFdpZHRoKHJlc3RXaWR0aCk7XG4gIH1cbiAgZnVuY3Rpb24gcmVnaXN0ZXJTdWZmaXhTaXplKF8sIHdpZHRoKSB7XG4gICAgc2V0U3VmZml4V2lkdGgod2lkdGgpO1xuICB9XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gRWZmZWN0ID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIGZ1bmN0aW9uIGdldEl0ZW1XaWR0aChpbmRleCkge1xuICAgIHJldHVybiBpdGVtV2lkdGhzLmdldChnZXRLZXkobWVyZ2VkRGF0YVtpbmRleF0sIGluZGV4KSk7XG4gIH1cbiAgdXNlTGF5b3V0RWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAobWVyZ2VkQ29udGFpbmVyV2lkdGggJiYgdHlwZW9mIG1lcmdlZFJlc3RXaWR0aCA9PT0gJ251bWJlcicgJiYgbWVyZ2VkRGF0YSkge1xuICAgICAgdmFyIHRvdGFsV2lkdGggPSBzdWZmaXhXaWR0aDtcbiAgICAgIHZhciBsZW4gPSBtZXJnZWREYXRhLmxlbmd0aDtcbiAgICAgIHZhciBsYXN0SW5kZXggPSBsZW4gLSAxO1xuXG4gICAgICAvLyBXaGVuIGRhdGEgY291bnQgY2hhbmdlIHRvIDAsIHJlc2V0IHRoaXMgc2luY2Ugbm90IGxvb3Agd2lsbCByZWFjaFxuICAgICAgaWYgKCFsZW4pIHtcbiAgICAgICAgdXBkYXRlRGlzcGxheUNvdW50KDAsIG51bGwpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbjsgaSArPSAxKSB7XG4gICAgICAgIHZhciBjdXJyZW50SXRlbVdpZHRoID0gZ2V0SXRlbVdpZHRoKGkpO1xuXG4gICAgICAgIC8vIEZ1bGx5IHdpbGwgYWx3YXlzIHJlbmRlclxuICAgICAgICBpZiAoZnVsbHlTU1IpIHtcbiAgICAgICAgICBjdXJyZW50SXRlbVdpZHRoID0gY3VycmVudEl0ZW1XaWR0aCB8fCAwO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQnJlYWsgc2luY2UgZGF0YSBub3QgcmVhZHlcbiAgICAgICAgaWYgKGN1cnJlbnRJdGVtV2lkdGggPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgIHVwZGF0ZURpc3BsYXlDb3VudChpIC0gMSwgdW5kZWZpbmVkLCB0cnVlKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEZpbmQgYmVzdCBtYXRjaFxuICAgICAgICB0b3RhbFdpZHRoICs9IGN1cnJlbnRJdGVtV2lkdGg7XG4gICAgICAgIGlmIChcbiAgICAgICAgLy8gT25seSBvbmUgbWVhbnMgYHRvdGFsV2lkdGhgIGlzIHRoZSBmaW5hbCB3aWR0aFxuICAgICAgICBsYXN0SW5kZXggPT09IDAgJiYgdG90YWxXaWR0aCA8PSBtZXJnZWRDb250YWluZXJXaWR0aCB8fFxuICAgICAgICAvLyBMYXN0IHR3byB3aWR0aCB3aWxsIGJlIHRoZSBmaW5hbCB3aWR0aFxuICAgICAgICBpID09PSBsYXN0SW5kZXggLSAxICYmIHRvdGFsV2lkdGggKyBnZXRJdGVtV2lkdGgobGFzdEluZGV4KSA8PSBtZXJnZWRDb250YWluZXJXaWR0aCkge1xuICAgICAgICAgIC8vIEFkZGl0aW9uYWwgY2hlY2sgaWYgbWF0Y2ggdGhlIGVuZFxuICAgICAgICAgIHVwZGF0ZURpc3BsYXlDb3VudChsYXN0SW5kZXgsIG51bGwpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9IGVsc2UgaWYgKHRvdGFsV2lkdGggKyBtZXJnZWRSZXN0V2lkdGggPiBtZXJnZWRDb250YWluZXJXaWR0aCkge1xuICAgICAgICAgIC8vIENhbiBub3QgaG9sZCBhbGwgdGhlIGNvbnRlbnQgdG8gc2hvdyByZXN0XG4gICAgICAgICAgdXBkYXRlRGlzcGxheUNvdW50KGkgLSAxLCB0b3RhbFdpZHRoIC0gY3VycmVudEl0ZW1XaWR0aCAtIHN1ZmZpeFdpZHRoICsgcmVzdFdpZHRoKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKHN1ZmZpeCAmJiBnZXRJdGVtV2lkdGgoMCkgKyBzdWZmaXhXaWR0aCA+IG1lcmdlZENvbnRhaW5lcldpZHRoKSB7XG4gICAgICAgIHNldFN1ZmZpeEZpeGVkU3RhcnQobnVsbCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbbWVyZ2VkQ29udGFpbmVyV2lkdGgsIGl0ZW1XaWR0aHMsIHJlc3RXaWR0aCwgc3VmZml4V2lkdGgsIGdldEtleSwgbWVyZ2VkRGF0YV0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IFJlbmRlciA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgZGlzcGxheVJlc3QgPSByZXN0UmVhZHkgJiYgISFvbWl0dGVkSXRlbXMubGVuZ3RoO1xuICB2YXIgc3VmZml4U3R5bGUgPSB7fTtcbiAgaWYgKHN1ZmZpeEZpeGVkU3RhcnQgIT09IG51bGwgJiYgc2hvdWxkUmVzcG9uc2l2ZSkge1xuICAgIHN1ZmZpeFN0eWxlID0ge1xuICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICBsZWZ0OiBzdWZmaXhGaXhlZFN0YXJ0LFxuICAgICAgdG9wOiAwXG4gICAgfTtcbiAgfVxuICB2YXIgaXRlbVNoYXJlZFByb3BzID0ge1xuICAgIHByZWZpeENsczogaXRlbVByZWZpeENscyxcbiAgICByZXNwb25zaXZlOiBzaG91bGRSZXNwb25zaXZlLFxuICAgIGNvbXBvbmVudDogaXRlbUNvbXBvbmVudCxcbiAgICBpbnZhbGlkYXRlOiBpbnZhbGlkYXRlXG4gIH07XG5cbiAgLy8gPj4+Pj4gQ2hvaWNlIHJlbmRlciBmdW4gYnkgYHJlbmRlclJhd0l0ZW1gXG4gIHZhciBpbnRlcm5hbFJlbmRlckl0ZW1Ob2RlID0gcmVuZGVyUmF3SXRlbSA/IGZ1bmN0aW9uIChpdGVtLCBpbmRleCkge1xuICAgIHZhciBrZXkgPSBnZXRLZXkoaXRlbSwgaW5kZXgpO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChPdmVyZmxvd0NvbnRleHQuUHJvdmlkZXIsIHtcbiAgICAgIGtleToga2V5LFxuICAgICAgdmFsdWU6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgaXRlbVNoYXJlZFByb3BzKSwge30sIHtcbiAgICAgICAgb3JkZXI6IGluZGV4LFxuICAgICAgICBpdGVtOiBpdGVtLFxuICAgICAgICBpdGVtS2V5OiBrZXksXG4gICAgICAgIHJlZ2lzdGVyU2l6ZTogcmVnaXN0ZXJTaXplLFxuICAgICAgICBkaXNwbGF5OiBpbmRleCA8PSBtZXJnZWREaXNwbGF5Q291bnRcbiAgICAgIH0pXG4gICAgfSwgcmVuZGVyUmF3SXRlbShpdGVtLCBpbmRleCkpO1xuICB9IDogZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7XG4gICAgdmFyIGtleSA9IGdldEtleShpdGVtLCBpbmRleCk7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEl0ZW0sIF9leHRlbmRzKHt9LCBpdGVtU2hhcmVkUHJvcHMsIHtcbiAgICAgIG9yZGVyOiBpbmRleCxcbiAgICAgIGtleToga2V5LFxuICAgICAgaXRlbTogaXRlbSxcbiAgICAgIHJlbmRlckl0ZW06IG1lcmdlZFJlbmRlckl0ZW0sXG4gICAgICBpdGVtS2V5OiBrZXksXG4gICAgICByZWdpc3RlclNpemU6IHJlZ2lzdGVyU2l6ZSxcbiAgICAgIGRpc3BsYXk6IGluZGV4IDw9IG1lcmdlZERpc3BsYXlDb3VudFxuICAgIH0pKTtcbiAgfTtcblxuICAvLyA+Pj4+PiBSZXN0IG5vZGVcbiAgdmFyIHJlc3RDb250ZXh0UHJvcHMgPSB7XG4gICAgb3JkZXI6IGRpc3BsYXlSZXN0ID8gbWVyZ2VkRGlzcGxheUNvdW50IDogTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIsXG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChpdGVtUHJlZml4Q2xzLCBcIi1yZXN0XCIpLFxuICAgIHJlZ2lzdGVyU2l6ZTogcmVnaXN0ZXJPdmVyZmxvd1NpemUsXG4gICAgZGlzcGxheTogZGlzcGxheVJlc3RcbiAgfTtcbiAgdmFyIG1lcmdlZFJlbmRlclJlc3QgPSByZW5kZXJSZXN0IHx8IGRlZmF1bHRSZW5kZXJSZXN0O1xuICB2YXIgcmVzdE5vZGUgPSByZW5kZXJSYXdSZXN0ID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoT3ZlcmZsb3dDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWU6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgaXRlbVNoYXJlZFByb3BzKSwgcmVzdENvbnRleHRQcm9wcylcbiAgfSwgcmVuZGVyUmF3UmVzdChvbWl0dGVkSXRlbXMpKSA6IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEl0ZW0sIF9leHRlbmRzKHt9LCBpdGVtU2hhcmVkUHJvcHMsIHJlc3RDb250ZXh0UHJvcHMpLCB0eXBlb2YgbWVyZ2VkUmVuZGVyUmVzdCA9PT0gJ2Z1bmN0aW9uJyA/IG1lcmdlZFJlbmRlclJlc3Qob21pdHRlZEl0ZW1zKSA6IG1lcmdlZFJlbmRlclJlc3QpO1xuICB2YXIgb3ZlcmZsb3dOb2RlID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ29tcG9uZW50LCBfZXh0ZW5kcyh7XG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKCFpbnZhbGlkYXRlICYmIHByZWZpeENscywgY2xhc3NOYW1lKSxcbiAgICBzdHlsZTogc3R5bGUsXG4gICAgcmVmOiByZWZcbiAgfSwgcmVzdFByb3BzKSwgbWVyZ2VkRGF0YS5tYXAoaW50ZXJuYWxSZW5kZXJJdGVtTm9kZSksIHNob3dSZXN0ID8gcmVzdE5vZGUgOiBudWxsLCBzdWZmaXggJiYgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoSXRlbSwgX2V4dGVuZHMoe30sIGl0ZW1TaGFyZWRQcm9wcywge1xuICAgIHJlc3BvbnNpdmU6IGlzUmVzcG9uc2l2ZSxcbiAgICByZXNwb25zaXZlRGlzYWJsZWQ6ICFzaG91bGRSZXNwb25zaXZlLFxuICAgIG9yZGVyOiBtZXJnZWREaXNwbGF5Q291bnQsXG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChpdGVtUHJlZml4Q2xzLCBcIi1zdWZmaXhcIiksXG4gICAgcmVnaXN0ZXJTaXplOiByZWdpc3RlclN1ZmZpeFNpemUsXG4gICAgZGlzcGxheTogdHJ1ZSxcbiAgICBzdHlsZTogc3VmZml4U3R5bGVcbiAgfSksIHN1ZmZpeCkpO1xuICByZXR1cm4gaXNSZXNwb25zaXZlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUmVzaXplT2JzZXJ2ZXIsIHtcbiAgICBvblJlc2l6ZTogb25PdmVyZmxvd1Jlc2l6ZSxcbiAgICBkaXNhYmxlZDogIXNob3VsZFJlc3BvbnNpdmVcbiAgfSwgb3ZlcmZsb3dOb2RlKSA6IG92ZXJmbG93Tm9kZTtcbn1cbnZhciBGb3J3YXJkT3ZlcmZsb3cgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihPdmVyZmxvdyk7XG5Gb3J3YXJkT3ZlcmZsb3cuZGlzcGxheU5hbWUgPSAnT3ZlcmZsb3cnO1xuRm9yd2FyZE92ZXJmbG93Lkl0ZW0gPSBSYXdJdGVtO1xuRm9yd2FyZE92ZXJmbG93LlJFU1BPTlNJVkUgPSBSRVNQT05TSVZFO1xuRm9yd2FyZE92ZXJmbG93LklOVkFMSURBVEUgPSBJTlZBTElEQVRFO1xuXG4vLyBDb252ZXJ0IHRvIGdlbmVyaWMgdHlwZVxuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZE92ZXJmbG93OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Overflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/RawItem.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/RawItem.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\n\n\n\n\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext);\n\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(context, _excluded2);\n  var className = props.className,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded3);\n\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_Item__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RawItem);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvUmF3SXRlbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDZ0M7QUFDMUY7QUFDQTtBQUNBO0FBQytCO0FBQ0s7QUFDVjtBQUNrQjtBQUM1QztBQUNBLGdCQUFnQiw2Q0FBZ0IsQ0FBQyxxREFBZTs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOEZBQXdCO0FBQzNDLHdCQUF3QixnREFBbUIsWUFBWSw4RUFBUSxHQUFHO0FBQ2xFO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxrQkFBa0IsOEZBQXdCO0FBQzFDO0FBQ0EsZ0JBQWdCLDhGQUF3Qjs7QUFFeEM7QUFDQSxzQkFBc0IsZ0RBQW1CLENBQUMscURBQWU7QUFDekQ7QUFDQSxHQUFHLGVBQWUsZ0RBQW1CLENBQUMsNkNBQUksRUFBRSw4RUFBUTtBQUNwRDtBQUNBLGVBQWUsaURBQVU7QUFDekIsR0FBRztBQUNIO0FBQ0EsMkJBQTJCLDZDQUFnQjtBQUMzQztBQUNBLGlFQUFlLE9BQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLW92ZXJmbG93XFxlc1xcUmF3SXRlbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiY29tcG9uZW50XCJdLFxuICBfZXhjbHVkZWQyID0gW1wiY2xhc3NOYW1lXCJdLFxuICBfZXhjbHVkZWQzID0gW1wiY2xhc3NOYW1lXCJdO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgSXRlbSBmcm9tIFwiLi9JdGVtXCI7XG5pbXBvcnQgeyBPdmVyZmxvd0NvbnRleHQgfSBmcm9tIFwiLi9jb250ZXh0XCI7XG52YXIgSW50ZXJuYWxSYXdJdGVtID0gZnVuY3Rpb24gSW50ZXJuYWxSYXdJdGVtKHByb3BzLCByZWYpIHtcbiAgdmFyIGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KE92ZXJmbG93Q29udGV4dCk7XG5cbiAgLy8gUmVuZGVyIGRpcmVjdGx5IHdoZW4gY29udGV4dCBub3QgcHJvdmlkZWRcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdmFyIF9wcm9wcyRjb21wb25lbnQgPSBwcm9wcy5jb21wb25lbnQsXG4gICAgICBDb21wb25lbnQgPSBfcHJvcHMkY29tcG9uZW50ID09PSB2b2lkIDAgPyAnZGl2JyA6IF9wcm9wcyRjb21wb25lbnQsXG4gICAgICBfcmVzdFByb3BzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKHByb3BzLCBfZXhjbHVkZWQpO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDb21wb25lbnQsIF9leHRlbmRzKHt9LCBfcmVzdFByb3BzLCB7XG4gICAgICByZWY6IHJlZlxuICAgIH0pKTtcbiAgfVxuICB2YXIgY29udGV4dENsYXNzTmFtZSA9IGNvbnRleHQuY2xhc3NOYW1lLFxuICAgIHJlc3RDb250ZXh0ID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKGNvbnRleHQsIF9leGNsdWRlZDIpO1xuICB2YXIgY2xhc3NOYW1lID0gcHJvcHMuY2xhc3NOYW1lLFxuICAgIHJlc3RQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhwcm9wcywgX2V4Y2x1ZGVkMyk7XG5cbiAgLy8gRG8gbm90IHBhc3MgY29udGV4dCB0byBzdWIgaXRlbSB0byBhdm9pZCBtdWx0aXBsZSBtZWFzdXJlXG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChPdmVyZmxvd0NvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogbnVsbFxuICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChJdGVtLCBfZXh0ZW5kcyh7XG4gICAgcmVmOiByZWYsXG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKGNvbnRleHRDbGFzc05hbWUsIGNsYXNzTmFtZSlcbiAgfSwgcmVzdENvbnRleHQsIHJlc3RQcm9wcykpKTtcbn07XG52YXIgUmF3SXRlbSA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKEludGVybmFsUmF3SXRlbSk7XG5SYXdJdGVtLmRpc3BsYXlOYW1lID0gJ1Jhd0l0ZW0nO1xuZXhwb3J0IGRlZmF1bHQgUmF3SXRlbTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/RawItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/context.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* binding */ OverflowContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar OverflowContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEI7QUFDbkIsbUNBQW1DLDBEQUFtQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcaXplZmUtZm9ybS15ZW5pXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtb3ZlcmZsb3dcXGVzXFxjb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIE92ZXJmbG93Q29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KG51bGwpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/channelUpdate.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ channelUpdate)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n\nfunction channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaG9va3MvY2hhbm5lbFVwZGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQztBQUNsQjtBQUNmO0FBQ0EsSUFBSSwwREFBRztBQUNQLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcaXplZmUtZm9ybS15ZW5pXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtb3ZlcmZsb3dcXGVzXFxob29rc1xcY2hhbm5lbFVwZGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcmFmIGZyb20gXCJyYy11dGlsL2VzL3JhZlwiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY2hhbm5lbFVwZGF0ZShjYWxsYmFjaykge1xuICBpZiAodHlwZW9mIE1lc3NhZ2VDaGFubmVsID09PSAndW5kZWZpbmVkJykge1xuICAgIHJhZihjYWxsYmFjayk7XG4gIH0gZWxzZSB7XG4gICAgdmFyIGNoYW5uZWwgPSBuZXcgTWVzc2FnZUNoYW5uZWwoKTtcbiAgICBjaGFubmVsLnBvcnQxLm9ubWVzc2FnZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBjYWxsYmFjaygpO1xuICAgIH07XG4gICAgY2hhbm5lbC5wb3J0Mi5wb3N0TWVzc2FnZSh1bmRlZmluZWQpO1xuICB9XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/useEffectState.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEffectState),\n/* harmony export */   useBatcher: () => (/* binding */ useBatcher)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _channelUpdate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./channelUpdate */ \"(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\");\n\n\n\n\n\n/**\n * Batcher for record any `useEffectState` need update.\n */\nfunction useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      (0,_channelUpdate__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.unstable_batchedUpdates)(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nfunction useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(defaultValue),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-overflow/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Overflow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Overflow */ \"(ssr)/./node_modules/rc-overflow/es/Overflow.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Overflow__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLW92ZXJmbG93XFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE92ZXJmbG93IGZyb20gXCIuL092ZXJmbG93XCI7XG5leHBvcnQgZGVmYXVsdCBPdmVyZmxvdzsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/index.js\n");

/***/ })

};
;