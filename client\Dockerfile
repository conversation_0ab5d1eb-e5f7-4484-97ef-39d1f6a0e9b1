# client/Dockerfile (client klasörü içinde olmalı)

FROM node:20-alpine AS builder
WORKDIR /app                   

# Sadece client klasöründeki dosyaları /app'e kopyala.
# GitHub Actions'daki 'docker build' komutunun context'i proje kökü olduğu için,
# 'client/' öneki ile kopyalamalıyız.
COPY client/package.json ./package.json      
COPY client/package-lock.json ./package-lock.json 
# Ya da projeniz npm yerine yarn kullanıyorsa:
# COPY client/yarn.lock ./yarn.lock

RUN npm install

# Geri kalan tüm client dosyalarını kopyala (build aşaması için gerekli olanlar)
COPY client . 

RUN npm run build 


FROM node:20-alpine AS runner
WORKDIR /app
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public 
EXPOSE 3020
CMD ["npm", "start"]