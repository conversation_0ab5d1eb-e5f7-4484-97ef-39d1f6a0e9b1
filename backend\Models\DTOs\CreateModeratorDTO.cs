namespace backend.Models.DTOs;

/// <summary>
/// DTO for creating or updating a moderator user
/// </summary>
public class CreateModeratorDTO
{
    /// <summary>
    /// The full name of the moderator
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// The email address of the moderator
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// The password for the moderator account (optional for updates)
    /// </summary>
    public string? Password { get; set; } = null;
}
