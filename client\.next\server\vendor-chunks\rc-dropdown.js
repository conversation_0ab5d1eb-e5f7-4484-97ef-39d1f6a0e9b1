"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-dropdown";
exports.ids = ["vendor-chunks/rc-dropdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-dropdown/es/Dropdown.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-dropdown/es/Dropdown.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _rc_component_trigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @rc-component/trigger */ \"(ssr)/./node_modules/@rc-component/trigger/es/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./hooks/useAccessibility */ \"(ssr)/./node_modules/rc-dropdown/es/hooks/useAccessibility.js\");\n/* harmony import */ var _Overlay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Overlay */ \"(ssr)/./node_modules/rc-dropdown/es/Overlay.js\");\n/* harmony import */ var _placements__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./placements */ \"(ssr)/./node_modules/rc-dropdown/es/placements.js\");\n\n\n\n\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\", \"overlay\", \"children\", \"onVisibleChange\"];\n\n\n\n\n\n\n\nfunction Dropdown(props, ref) {\n  var _children$props;\n  var _props$arrow = props.arrow,\n    arrow = _props$arrow === void 0 ? false : _props$arrow,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    align = props.align,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n    _props$placements = props.placements,\n    placements = _props$placements === void 0 ? _placements__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : _props$placements,\n    getPopupContainer = props.getPopupContainer,\n    showAction = props.showAction,\n    hideAction = props.hideAction,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    visible = props.visible,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    otherProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_7___default().useState(),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    triggerVisible = _React$useState2[0],\n    setTriggerVisible = _React$useState2[1];\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = react__WEBPACK_IMPORTED_MODULE_7___default().useRef(null);\n  var overlayRef = react__WEBPACK_IMPORTED_MODULE_7___default().useRef(null);\n  var childRef = react__WEBPACK_IMPORTED_MODULE_7___default().useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_7___default().useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var handleVisibleChange = function handleVisibleChange(newVisible) {\n    setTriggerVisible(newVisible);\n    onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(newVisible);\n  };\n  (0,_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    visible: mergedVisible,\n    triggerRef: childRef,\n    onVisibleChange: handleVisibleChange,\n    autoFocus: autoFocus,\n    overlayRef: overlayRef\n  });\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n  var getMenuElement = function getMenuElement() {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().createElement(_Overlay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: overlayRef,\n      overlay: overlay,\n      prefixCls: prefixCls,\n      arrow: arrow\n    });\n  };\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n    return getMenuElement();\n  };\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n      alignPoint = props.alignPoint;\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n    return !alignPoint;\n  };\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n    return \"\".concat(prefixCls, \"-open\");\n  };\n  var childrenNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().cloneElement(children, {\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()((_children$props = children.props) === null || _children$props === void 0 ? void 0 : _children$props.className, mergedVisible && getOpenClassName()),\n    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(children) ? (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.composeRef)(childRef, (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.getNodeRef)(children)) : undefined\n  });\n  var triggerHideAction = hideAction;\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().createElement(_rc_component_trigger__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    builtinPlacements: placements\n  }, otherProps, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classnames__WEBPACK_IMPORTED_MODULE_5___default()(overlayClassName, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction,\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: handleVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), childrenNode);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().forwardRef(Dropdown));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/Dropdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/Overlay.js":
/*!************************************************!*\
  !*** ./node_modules/rc-dropdown/es/Overlay.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar Overlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.composeRef)(ref, (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.getNodeRef)(overlayNode));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, arrow && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(overlayNode, {\n    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.supportRef)(overlayNode) ? composedRef : undefined\n  }));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Overlay);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvT3ZlcmxheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9FO0FBQ2pCO0FBQ25ELDJCQUEyQixpREFBVTtBQUNyQztBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsOENBQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxvQkFBb0IsMERBQVUsTUFBTSwwREFBVTtBQUM5QyxzQkFBc0IsMERBQW1CLENBQUMsdURBQWMsOEJBQThCLDBEQUFtQjtBQUN6RztBQUNBLEdBQUcsZ0JBQWdCLHlEQUFrQjtBQUNyQyxTQUFTLDBEQUFVO0FBQ25CLEdBQUc7QUFDSCxDQUFDO0FBQ0QsaUVBQWUsT0FBTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcaXplZmUtZm9ybS15ZW5pXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtZHJvcGRvd25cXGVzXFxPdmVybGF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbXBvc2VSZWYsIGdldE5vZGVSZWYsIHN1cHBvcnRSZWYgfSBmcm9tIFwicmMtdXRpbC9lcy9yZWZcIjtcbmltcG9ydCBSZWFjdCwgeyBmb3J3YXJkUmVmLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xudmFyIE92ZXJsYXkgPSAvKiNfX1BVUkVfXyovZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikge1xuICB2YXIgb3ZlcmxheSA9IHByb3BzLm92ZXJsYXksXG4gICAgYXJyb3cgPSBwcm9wcy5hcnJvdyxcbiAgICBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHM7XG4gIHZhciBvdmVybGF5Tm9kZSA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHZhciBvdmVybGF5RWxlbWVudDtcbiAgICBpZiAodHlwZW9mIG92ZXJsYXkgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgIG92ZXJsYXlFbGVtZW50ID0gb3ZlcmxheSgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBvdmVybGF5RWxlbWVudCA9IG92ZXJsYXk7XG4gICAgfVxuICAgIHJldHVybiBvdmVybGF5RWxlbWVudDtcbiAgfSwgW292ZXJsYXldKTtcbiAgdmFyIGNvbXBvc2VkUmVmID0gY29tcG9zZVJlZihyZWYsIGdldE5vZGVSZWYob3ZlcmxheU5vZGUpKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlYWN0LkZyYWdtZW50LCBudWxsLCBhcnJvdyAmJiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWFycm93XCIpXG4gIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY2xvbmVFbGVtZW50KG92ZXJsYXlOb2RlLCB7XG4gICAgcmVmOiBzdXBwb3J0UmVmKG92ZXJsYXlOb2RlKSA/IGNvbXBvc2VkUmVmIDogdW5kZWZpbmVkXG4gIH0pKTtcbn0pO1xuZXhwb3J0IGRlZmF1bHQgT3ZlcmxheTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/Overlay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/hooks/useAccessibility.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-dropdown/es/hooks/useAccessibility.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAccessibility)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar ESC = rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_0__[\"default\"].ESC,\n  TAB = rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_0__[\"default\"].TAB;\nfunction useAccessibility(_ref) {\n  var visible = _ref.visible,\n    triggerRef = _ref.triggerRef,\n    onVisibleChange = _ref.onVisibleChange,\n    autoFocus = _ref.autoFocus,\n    overlayRef = _ref.overlayRef;\n  var focusMenuRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible) {\n      var _triggerRef$current, _triggerRef$current$f;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 || (_triggerRef$current$f = _triggerRef$current.focus) === null || _triggerRef$current$f === void 0 || _triggerRef$current$f.call(_triggerRef$current);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(false);\n    }\n  };\n  var focusMenu = function focusMenu() {\n    var _overlayRef$current;\n    if ((_overlayRef$current = overlayRef.current) !== null && _overlayRef$current !== void 0 && _overlayRef$current.focus) {\n      overlayRef.current.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n    return false;\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        {\n          var focusResult = false;\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n          break;\n        }\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    if (visible) {\n      window.addEventListener(\"keydown\", handleKeyDown);\n      if (autoFocus) {\n        // FIXME: hack with raf\n        (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(focusMenu, 3);\n      }\n      return function () {\n        window.removeEventListener(\"keydown\", handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvaG9va3MvdXNlQWNjZXNzaWJpbGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF5QztBQUNSO0FBQ0Y7QUFDL0IsVUFBVSwwREFBTztBQUNqQixRQUFRLDBEQUFPO0FBQ0E7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHlDQUFZO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMERBQUc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLGNBQWM7QUFDakIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLWRyb3Bkb3duXFxlc1xcaG9va3NcXHVzZUFjY2Vzc2liaWxpdHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEtleUNvZGUgZnJvbSBcInJjLXV0aWwvZXMvS2V5Q29kZVwiO1xuaW1wb3J0IHJhZiBmcm9tIFwicmMtdXRpbC9lcy9yYWZcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIEVTQyA9IEtleUNvZGUuRVNDLFxuICBUQUIgPSBLZXlDb2RlLlRBQjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUFjY2Vzc2liaWxpdHkoX3JlZikge1xuICB2YXIgdmlzaWJsZSA9IF9yZWYudmlzaWJsZSxcbiAgICB0cmlnZ2VyUmVmID0gX3JlZi50cmlnZ2VyUmVmLFxuICAgIG9uVmlzaWJsZUNoYW5nZSA9IF9yZWYub25WaXNpYmxlQ2hhbmdlLFxuICAgIGF1dG9Gb2N1cyA9IF9yZWYuYXV0b0ZvY3VzLFxuICAgIG92ZXJsYXlSZWYgPSBfcmVmLm92ZXJsYXlSZWY7XG4gIHZhciBmb2N1c01lbnVSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICB2YXIgaGFuZGxlQ2xvc2VNZW51QW5kUmV0dXJuRm9jdXMgPSBmdW5jdGlvbiBoYW5kbGVDbG9zZU1lbnVBbmRSZXR1cm5Gb2N1cygpIHtcbiAgICBpZiAodmlzaWJsZSkge1xuICAgICAgdmFyIF90cmlnZ2VyUmVmJGN1cnJlbnQsIF90cmlnZ2VyUmVmJGN1cnJlbnQkZjtcbiAgICAgIChfdHJpZ2dlclJlZiRjdXJyZW50ID0gdHJpZ2dlclJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfdHJpZ2dlclJlZiRjdXJyZW50ID09PSB2b2lkIDAgfHwgKF90cmlnZ2VyUmVmJGN1cnJlbnQkZiA9IF90cmlnZ2VyUmVmJGN1cnJlbnQuZm9jdXMpID09PSBudWxsIHx8IF90cmlnZ2VyUmVmJGN1cnJlbnQkZiA9PT0gdm9pZCAwIHx8IF90cmlnZ2VyUmVmJGN1cnJlbnQkZi5jYWxsKF90cmlnZ2VyUmVmJGN1cnJlbnQpO1xuICAgICAgb25WaXNpYmxlQ2hhbmdlID09PSBudWxsIHx8IG9uVmlzaWJsZUNoYW5nZSA9PT0gdm9pZCAwIHx8IG9uVmlzaWJsZUNoYW5nZShmYWxzZSk7XG4gICAgfVxuICB9O1xuICB2YXIgZm9jdXNNZW51ID0gZnVuY3Rpb24gZm9jdXNNZW51KCkge1xuICAgIHZhciBfb3ZlcmxheVJlZiRjdXJyZW50O1xuICAgIGlmICgoX292ZXJsYXlSZWYkY3VycmVudCA9IG92ZXJsYXlSZWYuY3VycmVudCkgIT09IG51bGwgJiYgX292ZXJsYXlSZWYkY3VycmVudCAhPT0gdm9pZCAwICYmIF9vdmVybGF5UmVmJGN1cnJlbnQuZm9jdXMpIHtcbiAgICAgIG92ZXJsYXlSZWYuY3VycmVudC5mb2N1cygpO1xuICAgICAgZm9jdXNNZW51UmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfTtcbiAgdmFyIGhhbmRsZUtleURvd24gPSBmdW5jdGlvbiBoYW5kbGVLZXlEb3duKGV2ZW50KSB7XG4gICAgc3dpdGNoIChldmVudC5rZXlDb2RlKSB7XG4gICAgICBjYXNlIEVTQzpcbiAgICAgICAgaGFuZGxlQ2xvc2VNZW51QW5kUmV0dXJuRm9jdXMoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFRBQjpcbiAgICAgICAge1xuICAgICAgICAgIHZhciBmb2N1c1Jlc3VsdCA9IGZhbHNlO1xuICAgICAgICAgIGlmICghZm9jdXNNZW51UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgIGZvY3VzUmVzdWx0ID0gZm9jdXNNZW51KCk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChmb2N1c1Jlc3VsdCkge1xuICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgaGFuZGxlQ2xvc2VNZW51QW5kUmV0dXJuRm9jdXMoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICB9XG4gIH07XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKHZpc2libGUpIHtcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duKTtcbiAgICAgIGlmIChhdXRvRm9jdXMpIHtcbiAgICAgICAgLy8gRklYTUU6IGhhY2sgd2l0aCByYWZcbiAgICAgICAgcmFmKGZvY3VzTWVudSwgMyk7XG4gICAgICB9XG4gICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93bik7XG4gICAgICAgIGZvY3VzTWVudVJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgZm9jdXNNZW51UmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9O1xuICB9LCBbdmlzaWJsZV0pOyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/hooks/useAccessibility.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-dropdown/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Dropdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Dropdown */ \"(ssr)/./node_modules/rc-dropdown/es/Dropdown.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Dropdown__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYnVyYWtcXERlc2t0b3BcXGl6ZWZlLWZvcm0teWVuaVxcY2xpZW50XFxub2RlX21vZHVsZXNcXHJjLWRyb3Bkb3duXFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IERyb3Bkb3duIGZyb20gXCIuL0Ryb3Bkb3duXCI7XG5leHBvcnQgZGVmYXVsdCBEcm9wZG93bjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/placements.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-dropdown/es/placements.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  top: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (placements);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvcGxhY2VtZW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsVUFBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxidXJha1xcRGVza3RvcFxcaXplZmUtZm9ybS15ZW5pXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xccmMtZHJvcGRvd25cXGVzXFxwbGFjZW1lbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBhdXRvQWRqdXN0T3ZlcmZsb3cgPSB7XG4gIGFkanVzdFg6IDEsXG4gIGFkanVzdFk6IDFcbn07XG52YXIgdGFyZ2V0T2Zmc2V0ID0gWzAsIDBdO1xudmFyIHBsYWNlbWVudHMgPSB7XG4gIHRvcExlZnQ6IHtcbiAgICBwb2ludHM6IFsnYmwnLCAndGwnXSxcbiAgICBvdmVyZmxvdzogYXV0b0FkanVzdE92ZXJmbG93LFxuICAgIG9mZnNldDogWzAsIC00XSxcbiAgICB0YXJnZXRPZmZzZXQ6IHRhcmdldE9mZnNldFxuICB9LFxuICB0b3A6IHtcbiAgICBwb2ludHM6IFsnYmMnLCAndGMnXSxcbiAgICBvdmVyZmxvdzogYXV0b0FkanVzdE92ZXJmbG93LFxuICAgIG9mZnNldDogWzAsIC00XSxcbiAgICB0YXJnZXRPZmZzZXQ6IHRhcmdldE9mZnNldFxuICB9LFxuICB0b3BSaWdodDoge1xuICAgIHBvaW50czogWydicicsICd0ciddLFxuICAgIG92ZXJmbG93OiBhdXRvQWRqdXN0T3ZlcmZsb3csXG4gICAgb2Zmc2V0OiBbMCwgLTRdLFxuICAgIHRhcmdldE9mZnNldDogdGFyZ2V0T2Zmc2V0XG4gIH0sXG4gIGJvdHRvbUxlZnQ6IHtcbiAgICBwb2ludHM6IFsndGwnLCAnYmwnXSxcbiAgICBvdmVyZmxvdzogYXV0b0FkanVzdE92ZXJmbG93LFxuICAgIG9mZnNldDogWzAsIDRdLFxuICAgIHRhcmdldE9mZnNldDogdGFyZ2V0T2Zmc2V0XG4gIH0sXG4gIGJvdHRvbToge1xuICAgIHBvaW50czogWyd0YycsICdiYyddLFxuICAgIG92ZXJmbG93OiBhdXRvQWRqdXN0T3ZlcmZsb3csXG4gICAgb2Zmc2V0OiBbMCwgNF0sXG4gICAgdGFyZ2V0T2Zmc2V0OiB0YXJnZXRPZmZzZXRcbiAgfSxcbiAgYm90dG9tUmlnaHQ6IHtcbiAgICBwb2ludHM6IFsndHInLCAnYnInXSxcbiAgICBvdmVyZmxvdzogYXV0b0FkanVzdE92ZXJmbG93LFxuICAgIG9mZnNldDogWzAsIDRdLFxuICAgIHRhcmdldE9mZnNldDogdGFyZ2V0T2Zmc2V0XG4gIH1cbn07XG5leHBvcnQgZGVmYXVsdCBwbGFjZW1lbnRzOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/placements.js\n");

/***/ })

};
;