// export const API_BASE_URL = 'https://fbapi.performa.nz/api';
export const API_BASE_URL = "http://localhost:3031/api";
// export const API_BASE_URL = 'http://**************/api';
// export const API_BASE_URL = 'https://apierkekbasketaltyapi.fenerbahce.org/api';

export const apiEndpoints = {
  submitForm: `${API_BASE_URL}/Form`,
  getApplications: `${API_BASE_URL}/User`,
  updateStatus: `${API_BASE_URL}/User`,
  auth: {
    login: `${API_BASE_URL}/Auth/login`,
  },
  // Rating endpoints
  getAllRatings: `${API_BASE_URL}/Rating/all`,
  getUserRatings: `${API_BASE_URL}/Rating/user`,
  getMyRatings: `${API_BASE_URL}/Rating/my-ratings`,
  getMyRatingForUser: `${API_BASE_URL}/Rating/my-rating`,
  createOrUpdateRating: `${API_BASE_URL}/Rating`,
  deleteRating: `${API_BASE_URL}/Rating`,
  // Form statistics endpoints
  getAgeStatistics: `${API_BASE_URL}/Form/age-statistics`,
};

export async function fetchApi(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  console.log(`Fetching API: ${endpoint}`);

  const defaultHeaders: Record<string, string> = {
    "Content-Type": "application/json",
  };

  const token = localStorage.getItem("fbAdminToken");
  if (token) {
    defaultHeaders["Authorization"] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  return fetch(endpoint, config);
}
